#!/usr/bin/env python3
"""
Test di regressione per verificare che i link ai documenti vengano sempre preservati
nelle risposte dell'assistente virtuale.

Questo test è stato creato per prevenire la regressione del bug dove i link ai documenti
venivano rimossi a causa di un errore nella gestione dei risultati dei guardrails di output.
"""

import unittest
import os
import sys
import re
from unittest.mock import Mock, patch
from dotenv import load_dotenv

# Aggiungi il percorso del progetto al PYTHONPATH
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pdf_chatbot_prodotti import ProductChatbot

class TestDocumentLinksRegression(unittest.TestCase):
    """Test di regressione per i link ai documenti"""
    
    @classmethod
    def setUpClass(cls):
        """Setup della classe di test"""
        load_dotenv()
        cls.jina_api_key = os.getenv("JINA_API_KEY")
        cls.gemini_api_key = os.getenv("GEMINI_API_KEY")
        
        if not cls.jina_api_key or not cls.gemini_api_key:
            raise unittest.SkipTest("API keys mancanti nel file .env")
    
    def setUp(self):
        """Setup per ogni test"""
        self.chatbot = ProductChatbot(
            jina_api_key=self.jina_api_key,
            gemini_api_key=self.gemini_api_key,
            verbosity_level=1  # Verbosità minima per i test
        )
        self.product_code = "ProdottoA"
        
        # Prepara i documenti se necessario
        self.chatbot.prepare_product_documents(self.product_code)
    
    def test_links_preserved_in_response(self):
        """Test che verifica che i link ai documenti vengano preservati"""
        
        # Query che dovrebbe generare riferimenti ai documenti
        query = "Come faccio la verifica visiva del sensore?"
        
        # Esegui la query
        answer, _ = self.chatbot.search_and_answer(query, self.product_code)
        
        # Verifica che la risposta contenga link ai documenti
        link_pattern = r'\[📄[^\]]+\]\([^)]+\)'
        links = re.findall(link_pattern, answer)
        
        # Asserzioni
        self.assertIsInstance(answer, str, "La risposta deve essere una stringa")
        self.assertGreater(len(answer), 0, "La risposta non deve essere vuota")
        
        # Se la risposta contiene riferimenti a documenti, devono esserci link
        has_document_references = any(word in answer.lower() for word in 
                                    ['manuale', 'documento', 'pag.', 'pagina'])
        
        if has_document_references:
            self.assertGreater(len(links), 0, 
                             f"La risposta contiene riferimenti ai documenti ma nessun link: {answer}")
            
            # Verifica che i link abbiano il formato corretto
            for link in links:
                self.assertRegex(link, r'\[📄[^\]]+\]\(/pdf/[^)]+\)', 
                               f"Link malformato: {link}")
    
    def test_guardrails_dont_remove_links(self):
        """Test che verifica che i guardrails non rimuovano i link"""
        
        # Simula una risposta con link ai documenti
        response_with_links = (
            "Per la manutenzione consulta il "
            "[📄 Manuale uso e manutenzione.pdf - Pag. 17]"
            "(/pdf/ProdottoA/Manuale%20uso%20e%20manutenzione.pdf#page=17) "
            "per maggiori dettagli."
        )
        
        # Processa la risposta con i guardrails
        output_result = self.chatbot.guardrail_manager.process_output(response_with_links)
        
        # Verifica che i link siano ancora presenti
        processed_text = output_result.get("text", "")
        
        self.assertIn("[📄", processed_text, "I link ai documenti sono stati rimossi dai guardrails")
        self.assertIn("](/pdf/", processed_text, "I link ai documenti sono stati rimossi dai guardrails")
    
    def test_output_result_structure(self):
        """Test che verifica la struttura corretta del risultato dei guardrails di output"""
        
        test_text = "Testo di test"
        output_result = self.chatbot.guardrail_manager.process_output(test_text)
        
        # Verifica che il risultato abbia la struttura corretta
        self.assertIsInstance(output_result, dict, "Il risultato deve essere un dizionario")
        self.assertIn("text", output_result, "Il risultato deve contenere il campo 'text'")
        self.assertIn("blocked", output_result, "Il risultato deve contenere il campo 'blocked'")
        self.assertIn("modified", output_result, "Il risultato deve contenere il campo 'modified'")
        
        # Verifica che il testo sia preservato
        self.assertEqual(output_result["text"], test_text, 
                        "Il testo deve essere preservato quando non ci sono modifiche")
    
    def test_multiple_links_preservation(self):
        """Test che verifica la preservazione di più link nella stessa risposta"""
        
        # Simula una risposta con più link
        response_with_multiple_links = (
            "Consulta il [📄 Manuale 1.pdf - Pag. 10](/pdf/ProdottoA/Manuale1.pdf#page=10) "
            "e anche il [📄 Manuale 2.pdf - Pag. 20](/pdf/ProdottoA/Manuale2.pdf#page=20) "
            "per informazioni complete."
        )
        
        # Processa con i guardrails
        output_result = self.chatbot.guardrail_manager.process_output(response_with_multiple_links)
        processed_text = output_result.get("text", "")
        
        # Conta i link nella risposta originale e processata
        link_pattern = r'\[📄[^\]]+\]\([^)]+\)'
        original_links = re.findall(link_pattern, response_with_multiple_links)
        processed_links = re.findall(link_pattern, processed_text)
        
        self.assertEqual(len(original_links), len(processed_links), 
                        "Il numero di link deve rimanere invariato dopo il processing")
        self.assertEqual(len(processed_links), 2, "Devono essere preservati entrambi i link")

if __name__ == "__main__":
    unittest.main(verbosity=2)
