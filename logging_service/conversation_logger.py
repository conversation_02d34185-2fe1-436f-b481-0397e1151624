"""
Asynchronous conversation logging service with retry mechanism.
"""

import asyncio
import logging
import time
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
from queue import Queue, Empty
from threading import Thread, Event
from database import db_manager

logger = logging.getLogger(__name__)


class ConversationLogger:
    """Asynchronous conversation logger with retry mechanism."""
    
    def __init__(self, max_retries: int = 3, retry_delay: float = 1.0, queue_size: int = 1000):
        """
        Initialize the conversation logger.
        
        Args:
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retry attempts in seconds
            queue_size: Maximum size of the logging queue
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.queue = Queue(maxsize=queue_size)
        self.retry_queue = Queue()
        self.stop_event = Event()
        
        # Start worker threads
        self.worker_thread = Thread(target=self._worker, daemon=True)
        self.retry_thread = Thread(target=self._retry_worker, daemon=True)
        
        self.worker_thread.start()
        self.retry_thread.start()
        
        logger.info("ConversationLogger initialized and worker threads started")
    
    def log_conversation_async(self, conversation_data: Dict[str, Any]) -> bool:
        """
        Queue a conversation for asynchronous logging.
        
        Args:
            conversation_data: Dictionary containing conversation details
            
        Returns:
            True if successfully queued, False if queue is full
        """
        try:
            # Add metadata
            enriched_data = {
                **conversation_data,
                'log_id': str(uuid.uuid4()),
                'queued_at': datetime.utcnow().isoformat(),
                'retry_count': 0
            }
            
            self.queue.put_nowait(enriched_data)
            logger.debug(f"Conversation queued for logging: {enriched_data.get('log_id')}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to queue conversation for logging: {e}")
            return False
    
    def _worker(self):
        """Main worker thread for processing the logging queue."""
        logger.info("Logging worker thread started")
        
        while not self.stop_event.is_set():
            try:
                # Get item from queue with timeout
                conversation_data = self.queue.get(timeout=1.0)
                
                # Attempt to log the conversation
                success = self._log_conversation_sync(conversation_data)
                
                if not success:
                    # Add to retry queue if logging failed
                    self._add_to_retry_queue(conversation_data)
                
                self.queue.task_done()
                
            except Empty:
                # Timeout occurred, continue loop
                continue
            except Exception as e:
                logger.error(f"Error in logging worker: {e}")
    
    def _retry_worker(self):
        """Retry worker thread for handling failed logging attempts."""
        logger.info("Retry worker thread started")
        
        while not self.stop_event.is_set():
            try:
                # Get item from retry queue with timeout
                conversation_data = self.retry_queue.get(timeout=5.0)
                
                # Wait before retry
                time.sleep(self.retry_delay)
                
                # Attempt to log again
                success = self._log_conversation_sync(conversation_data)
                
                if not success:
                    # Check if we should retry again
                    retry_count = conversation_data.get('retry_count', 0)
                    if retry_count < self.max_retries:
                        conversation_data['retry_count'] = retry_count + 1
                        self._add_to_retry_queue(conversation_data)
                        logger.warning(f"Retry {retry_count + 1}/{self.max_retries} for log_id: {conversation_data.get('log_id')}")
                    else:
                        logger.error(f"Max retries exceeded for log_id: {conversation_data.get('log_id')}")
                
                self.retry_queue.task_done()
                
            except Empty:
                # Timeout occurred, continue loop
                continue
            except Exception as e:
                logger.error(f"Error in retry worker: {e}")
    
    def _log_conversation_sync(self, conversation_data: Dict[str, Any]) -> bool:
        """
        Synchronously log a conversation to the database.
        
        Args:
            conversation_data: Dictionary containing conversation details
            
        Returns:
            True if successful, False otherwise
        """
        try:
            record_id = db_manager.log_conversation(conversation_data)
            if record_id:
                logger.debug(f"Successfully logged conversation with ID: {record_id}")
                return True
            else:
                logger.warning(f"Failed to log conversation: {conversation_data.get('log_id')}")
                return False
                
        except Exception as e:
            logger.error(f"Error logging conversation: {e}")
            return False
    
    def _add_to_retry_queue(self, conversation_data: Dict[str, Any]):
        """Add conversation data to retry queue."""
        try:
            self.retry_queue.put_nowait(conversation_data)
        except Exception as e:
            logger.error(f"Failed to add to retry queue: {e}")
    
    def get_queue_status(self) -> Dict[str, int]:
        """Get current queue status."""
        return {
            'main_queue_size': self.queue.qsize(),
            'retry_queue_size': self.retry_queue.qsize()
        }
    
    def shutdown(self, timeout: float = 30.0):
        """
        Gracefully shutdown the logger.
        
        Args:
            timeout: Maximum time to wait for queues to empty
        """
        logger.info("Shutting down ConversationLogger...")
        
        # Signal threads to stop
        self.stop_event.set()
        
        # Wait for queues to empty
        start_time = time.time()
        while (self.queue.qsize() > 0 or self.retry_queue.qsize() > 0) and (time.time() - start_time) < timeout:
            time.sleep(0.1)
        
        # Wait for threads to finish
        if self.worker_thread.is_alive():
            self.worker_thread.join(timeout=5.0)
        
        if self.retry_thread.is_alive():
            self.retry_thread.join(timeout=5.0)
        
        logger.info("ConversationLogger shutdown complete")


# Global conversation logger instance
conversation_logger = ConversationLogger()
