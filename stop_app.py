#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to stop the Flask application gracefully.
Use this if the application doesn't respond to Ctrl+C.
"""

import os
import sys
import signal
import psutil
import time

def find_flask_processes():
    """Find running Flask processes."""
    flask_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info['cmdline']
            if cmdline and any('app.py' in arg or 'run_app.py' in arg for arg in cmdline):
                if 'python' in proc.info['name'].lower():
                    flask_processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return flask_processes

def stop_processes_gracefully(processes):
    """Stop processes gracefully."""
    print(f"Found {len(processes)} Flask process(es)")
    
    for proc in processes:
        try:
            print(f"Sending SIGTERM to process {proc.pid} ({' '.join(proc.cmdline())})")
            proc.send_signal(signal.SIGTERM)
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            print(f"Could not send signal to process {proc.pid}: {e}")
    
    # Wait for processes to terminate
    print("Waiting for processes to terminate gracefully...")
    gone, alive = psutil.wait_procs(processes, timeout=30)
    
    for proc in gone:
        print(f"✅ Process {proc.pid} terminated gracefully")
    
    # Force kill remaining processes
    if alive:
        print(f"Force killing {len(alive)} remaining process(es)...")
        for proc in alive:
            try:
                print(f"Force killing process {proc.pid}")
                proc.kill()
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                print(f"Could not kill process {proc.pid}: {e}")

def main():
    """Main function."""
    print("🔍 Looking for Flask application processes...")
    
    processes = find_flask_processes()
    
    if not processes:
        print("❌ No Flask application processes found")
        return
    
    try:
        stop_processes_gracefully(processes)
        print("✅ Flask application stopped successfully")
    except Exception as e:
        print(f"❌ Error stopping Flask application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
