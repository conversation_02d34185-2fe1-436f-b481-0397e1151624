-- Database setup script for chat-jina conversation logging
-- Run this script to create the database and table structure

-- <PERSON>reate database if it doesn't exist
CREATE DATABASE IF NOT EXISTS chat_jina CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the database
USE chat_jina;

-- Create conversations table
CREATE TABLE IF NOT EXISTS conversations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    product VARCHAR(255),
    question TEXT NOT NULL,
    response TEXT NOT NULL,
    guardrail_log JSON,
    session_id VARCHAR(255),
    user_agent TEXT,
    response_time_ms INT,
    confidence_score FLOAT,
    INDEX idx_timestamp (timestamp),
    INDEX idx_ip (ip_address),
    INDEX idx_product (product),
    INDEX idx_session (session_id)
);

-- Create user for the application (optional - adjust privileges as needed)
-- CREATE USER IF NOT EXISTS 'prova'@'localhost' IDENTIFIED BY 'prova';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON chat_jina.* TO 'prova'@'localhost';
-- FLUSH PRIVILEGES;

-- Show table structure
DESCRIBE conversations;
