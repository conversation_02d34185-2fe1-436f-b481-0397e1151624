#!/usr/bin/env python3
"""
Script di test per verificare le ottimizzazioni delle performance del sistema chat-jina.
"""

import os
import sys
import time
import asyncio
from dotenv import load_dotenv
from performance_config import PerformanceConfig, PerformanceProfiles

# Carica le variabili d'ambiente
load_dotenv()

def test_performance_config():
    """Testa le configurazioni di performance."""
    print("🔧 Test Configurazioni Performance")
    print("=" * 50)
    
    # Test configurazioni base
    print(f"✓ Timeout Gemini: {PerformanceConfig.GEMINI_TIMEOUT}s")
    print(f"✓ Cache Size: {PerformanceConfig.RESPONSE_CACHE_SIZE}")
    print(f"✓ Guardrails Paralleli: {PerformanceConfig.GUARDRAILS_PARALLEL}")
    print(f"✓ Ottimizzazione ChromaDB: {PerformanceConfig.CHROMADB_RESULTS_OPTIMIZATION}")
    
    # Test ottimizzazione risultati
    test_queries = [
        "help",
        "come funziona",
        "installazione prodotto",
        "procedura completa per configurazione sistema avanzato"
    ]
    
    print("\n📊 Test Ottimizzazione Risultati ChromaDB:")
    for query in test_queries:
        optimized = PerformanceConfig.optimize_search_results(query)
        words = len(query.split())
        print(f"  '{query}' ({words} parole) → {optimized} risultati")
    
    return True

def test_cache_performance():
    """Testa le performance del sistema di cache."""
    print("\n💾 Test Performance Cache")
    print("=" * 50)
    
    try:
        from pdf_chatbot_prodotti import ProductChatbot
        
        # Inizializza il chatbot (senza documenti per il test)
        jina_api_key = os.getenv('JINA_API_KEY')
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        
        if not jina_api_key or not gemini_api_key:
            print("❌ API keys non trovate. Configura JINA_API_KEY e GEMINI_API_KEY nel file .env")
            return False
        
        print("🔄 Inizializzazione chatbot...")
        chatbot = ProductChatbot(jina_api_key, gemini_api_key)
        
        # Test cache key generation
        test_query = "Come funziona questo prodotto?"
        test_context = "Contesto di test"
        test_history = []
        
        cache_key = chatbot._generate_cache_key(test_query, test_context, test_history)
        print(f"✓ Cache key generata: {cache_key[:16]}...")
        
        # Test similarity calculation
        query1 = "Come installare il prodotto"
        query2 = "Come si installa il prodotto"
        similarity = chatbot._calculate_query_similarity(query1, query2)
        print(f"✓ Similarità tra query simili: {similarity:.2f}")
        
        query3 = "Che tempo fa oggi"
        similarity2 = chatbot._calculate_query_similarity(query1, query3)
        print(f"✓ Similarità tra query diverse: {similarity2:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore nel test cache: {e}")
        return False

def test_guardrails_performance():
    """Testa le performance dei guardrails."""
    print("\n🛡️ Test Performance Guardrails")
    print("=" * 50)
    
    try:
        from guardrails_py.guardrail_manager import GuardrailManager
        from guardrails_py.input.input_length_control_guardrail import InputLengthControlGuardrail
        from guardrails_py.input.language_detection_guardrail import LanguageDetectionGuardrail
        
        # Test configurazione parallela
        config = PerformanceConfig.get_guardrails_config()
        print(f"✓ Configurazione guardrails: {config}")
        
        # Test esecuzione guardrails
        manager = GuardrailManager(
            input_guardrails=[
                InputLengthControlGuardrail(),
                LanguageDetectionGuardrail(),
            ],
            config=config
        )
        
        test_input = "Questa è una query di test per verificare le performance dei guardrails."
        
        # Test sequenziale
        start_time = time.time()
        result_seq = manager._execute_guardrails_sequential(manager.input_guardrails, test_input)
        seq_time = time.time() - start_time
        
        # Test parallelo
        start_time = time.time()
        result_par = manager._execute_guardrails_parallel(manager.input_guardrails, test_input)
        par_time = time.time() - start_time
        
        print(f"✓ Tempo esecuzione sequenziale: {seq_time:.3f}s")
        print(f"✓ Tempo esecuzione parallela: {par_time:.3f}s")
        print(f"✓ Speedup: {seq_time/par_time:.2f}x" if par_time > 0 else "✓ Speedup: N/A")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore nel test guardrails: {e}")
        return False

def test_profiles():
    """Testa i profili di performance predefiniti."""
    print("\n⚡ Test Profili Performance")
    print("=" * 50)
    
    # Salva configurazioni originali
    original_timeout = PerformanceConfig.GEMINI_TIMEOUT
    original_tokens = PerformanceConfig.GEMINI_MAX_OUTPUT_TOKENS
    
    try:
        # Test profilo veloce
        PerformanceProfiles.apply_fast_profile()
        print(f"✓ Profilo VELOCE - Timeout: {PerformanceConfig.GEMINI_TIMEOUT}s, Tokens: {PerformanceConfig.GEMINI_MAX_OUTPUT_TOKENS}")
        
        # Test profilo bilanciato
        PerformanceProfiles.apply_balanced_profile()
        print(f"✓ Profilo BILANCIATO - Timeout: {PerformanceConfig.GEMINI_TIMEOUT}s, Tokens: {PerformanceConfig.GEMINI_MAX_OUTPUT_TOKENS}")
        
        # Test profilo qualità
        PerformanceProfiles.apply_quality_profile()
        print(f"✓ Profilo QUALITÀ - Timeout: {PerformanceConfig.GEMINI_TIMEOUT}s, Tokens: {PerformanceConfig.GEMINI_MAX_OUTPUT_TOKENS}")
        
        return True
        
    finally:
        # Ripristina configurazioni originali
        PerformanceConfig.GEMINI_TIMEOUT = original_timeout
        PerformanceConfig.GEMINI_MAX_OUTPUT_TOKENS = original_tokens

def benchmark_response_time():
    """Benchmark dei tempi di risposta."""
    print("\n⏱️ Benchmark Tempi di Risposta")
    print("=" * 50)
    
    try:
        # Simula diversi scenari di carico
        scenarios = [
            ("Cache Hit", 0.001),  # Risposta dalla cache
            ("Query Semplice", 0.5),  # Query con pochi documenti
            ("Query Complessa", 2.0),  # Query con molti documenti
            ("Timeout Scenario", 30.0),  # Scenario di timeout
        ]
        
        for scenario, expected_time in scenarios:
            start_time = time.time()
            # Simula il tempo di elaborazione
            time.sleep(min(expected_time, 0.1))  # Simula solo per il test
            elapsed = time.time() - start_time
            
            status = "✓" if elapsed < expected_time else "⚠️"
            print(f"{status} {scenario}: {elapsed:.3f}s (target: {expected_time}s)")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore nel benchmark: {e}")
        return False

def main():
    """Funzione principale di test."""
    print("🚀 Test Suite Ottimizzazioni Performance Chat-Jina")
    print("=" * 60)
    
    tests = [
        ("Configurazioni Performance", test_performance_config),
        ("Performance Cache", test_cache_performance),
        ("Performance Guardrails", test_guardrails_performance),
        ("Profili Performance", test_profiles),
        ("Benchmark Tempi", benchmark_response_time),
    ]
    
    results = {}
    total_start = time.time()
    
    for test_name, test_func in tests:
        print(f"\n🧪 Esecuzione test: {test_name}")
        print("-" * 40)
        
        start_time = time.time()
        try:
            results[test_name] = test_func()
            elapsed = time.time() - start_time
            status = "✅ PASS" if results[test_name] else "❌ FAIL"
            print(f"{status} ({elapsed:.2f}s)")
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"❌ ERRORE: {e} ({elapsed:.2f}s)")
            results[test_name] = False
    
    # Riepilogo risultati
    total_elapsed = time.time() - total_start
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    print("\n" + "=" * 60)
    print("📊 RIEPILOGO RISULTATI")
    print("=" * 60)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Test completati: {passed}/{total}")
    print(f"⏱️ Tempo totale: {total_elapsed:.2f}s")
    
    if passed == total:
        print("🎉 Tutti i test sono passati! Le ottimizzazioni funzionano correttamente.")
        return True
    else:
        print("⚠️ Alcuni test sono falliti. Controlla i log per i dettagli.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
