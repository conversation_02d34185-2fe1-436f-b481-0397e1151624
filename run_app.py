#!/usr/bin/env python3
"""
Alternative launcher for the Flask application with better shutdown handling.
Use this if you have issues with Ctrl+C not working properly.
"""

import os
import sys
import signal
import logging
from threading import Event

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("chat.log"),
            logging.StreamHandler()
        ]
    )

def main():
    """Main function with proper shutdown handling."""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # Event to signal shutdown
    shutdown_event = Event()
    
    def signal_handler(signum, frame):
        """Handle shutdown signals."""
        logger.info(f"🛑 Received signal {signum}, initiating shutdown...")
        shutdown_event.set()
    
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Import Flask app components
        from app import app, conversation_logger
        
        logger.info("🚀 Starting Flask application with conversation logging...")
        
        # Start Flask in a separate thread
        from threading import Thread
        import time
        
        def run_flask():
            """Run Flask app in a thread."""
            try:
                app.run(debug=False, port=5001, use_reloader=False, threaded=True)
            except Exception as e:
                logger.error(f"Flask app error: {e}")
                shutdown_event.set()
        
        flask_thread = Thread(target=run_flask, daemon=True)
        flask_thread.start()
        
        logger.info("✅ Flask application started successfully")
        logger.info("🌐 Application available at: http://localhost:5001")
        logger.info("🛑 Press Ctrl+C to stop the application")
        
        # Wait for shutdown signal
        while not shutdown_event.is_set():
            try:
                shutdown_event.wait(timeout=1.0)
            except KeyboardInterrupt:
                logger.info("🛑 Keyboard interrupt received")
                break
        
        # Graceful shutdown
        logger.info("🔄 Shutting down application...")
        
        try:
            # Shutdown conversation logger
            conversation_logger.shutdown(timeout=30.0)
            logger.info("✅ Conversation logger shutdown complete")
        except Exception as e:
            logger.error(f"❌ Error shutting down conversation logger: {e}")
        
        logger.info("👋 Application shutdown complete")
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        logger.error("Make sure all dependencies are installed: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
