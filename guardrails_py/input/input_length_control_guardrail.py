
import time
import re

class InputLengthControlGuardrail:
    def __init__(self, config={}):
        self.config = {
            "max_characters": 10000,
            "max_words": 2000,
            "strategy": "truncate",  # 'reject', 'truncate', 'chunk'
            **config
        }

    def process(self, text, context={}):
        metrics = self._calculate_metrics(text)
        
        if self._is_within_limits(metrics):
            return {"action": "ALLOW", "details": {"metrics": metrics}}

        return self._handle_long_input(text, metrics)

    def _calculate_metrics(self, text):
        characters = len(text)
        words = len(re.findall(r'\w+', text))
        return {"characters": characters, "words": words}

    def _is_within_limits(self, metrics):
        return (
            metrics["characters"] <= self.config["max_characters"] and
            metrics["words"] <= self.config["max_words"]
        )

    def _handle_long_input(self, text, metrics):
        strategy = self.config["strategy"]
        if strategy == "reject":
            return {
                "action": "BLOCK",
                "reason": "Input too long",
                "details": {"metrics": metrics}
            }
        elif strategy == "truncate":
            truncated_text = self._smart_truncate(text)
            return {
                "action": "MODIFY",
                "modified_text": truncated_text,
                "warning": "Input was truncated to meet length limits."
            }
        # Other strategies like 'chunk' can be added here
        else: # Default to allow with a warning
            return {
                "action": "ALLOW",
                "warning": "Input is long but allowed by policy."
            }

    def _smart_truncate(self, text):
        max_chars = self.config["max_characters"]
        if len(text) <= max_chars:
            return text
        
        truncated = text[:max_chars]
        # Simple truncation at a word boundary
        last_space = truncated.rfind(' ')
        if last_space != -1:
            return truncated[:last_space] + "..."
        return truncated + "..."

