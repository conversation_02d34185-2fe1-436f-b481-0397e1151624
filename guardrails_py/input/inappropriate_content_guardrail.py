import re

class InappropriateContentGuardrail:
    def __init__(self, config={}):
        self.config = {
            "block_inappropriate_content": True,
            "confidence_threshold": 0.7,
            "categories": {
                "sexual_content": True,
                "adult_content": True,
                "inappropriate_requests": True,
            },
            **config
        }
        self.initialize_patterns()

    def initialize_patterns(self):
        """Initialize patterns for detecting inappropriate content in multiple languages"""
        self.inappropriate_patterns = {
            "sexual_content": [
                # Italian patterns
                r"\b(porno|pornografia|pornografico|pornografici|pornografiche)\b",
                r"\b(sesso|sessuale|sessuali|erotici?|erotica|erotiche)\b",
                r"\b(nud[oi]|nuda|nude|nudi)\b",
                r"\b(masturbazione|masturbare|masturbarsi)\b",
                r"\b(orgasmo|orgasmi|climax)\b",
                r"\b(genitali|pene|vagina|clitoride|testicoli)\b",
                r"\b(prostituzione|prostituta|prostitute|escort)\b",
                r"\b(bordello|bordelli|casa\s+chiusa)\b",
                
                # English patterns
                r"\b(porn|pornography|pornographic|adult\s+videos?)\b",
                r"\b(sex|sexual|erotic|nude|naked)\b",
                r"\b(masturbation|masturbate|orgasm|climax)\b",
                r"\b(genitals|penis|vagina|clitoris|testicles)\b",
                r"\b(prostitution|prostitute|escort|brothel)\b",
                r"\b(xxx|adult\s+content|nsfw)\b",
                
                # Common abbreviations and slang
                r"\b(xxx|nsfw|r18|18\+)\b",
                r"\b(milf|gilf|dilf)\b",
                r"\b(bdsm|fetish|kink)\b",
            ],
            "adult_content": [
                # Adult entertainment requests
                r"\b(film\s+porno|video\s+porno|contenuti?\s+per\s+adulti)\b",
                r"\b(siti?\s+porno|pornhub|xvideos|redtube)\b",
                r"\b(cam\s+girl|webcam\s+girl|onlyfans)\b",
                r"\b(strip\s+club|lap\s+dance|pole\s+dance)\b",
                
                # English equivalents
                r"\b(porn\s+(movies?|videos?|sites?))\b",
                r"\b(adult\s+(movies?|videos?|sites?))\b",
                r"\b(sex\s+(movies?|videos?|sites?))\b",
                r"\b(cam\s+(girl|boy|show))\b",
            ],
            "inappropriate_requests": [
                # Requests for inappropriate content
                r"\b(hai|avete|c['\']è|esiste|dove\s+trovo|come\s+trovo).{0,20}(porno|sesso|nud[oi])\b",
                r"\b(show\s+me|find\s+me|give\s+me|where\s+can\s+i\s+find).{0,20}(porn|sex|nude)\b",
                r"\b(voglio\s+vedere|fammi\s+vedere|mostrami).{0,20}(porno|sesso|nud[oi])\b",
                r"\b(want\s+to\s+see|show\s+me|let\s+me\s+see).{0,20}(porn|sex|nude)\b",
                
                # Dating/hookup requests
                r"\b(cerco\s+(sesso|avventure?|incontri?\s+piccanti?))\b",
                r"\b(looking\s+for\s+(sex|hookup|casual\s+encounter))\b",
                r"\b(one\s+night\s+stand|friends?\s+with\s+benefits)\b",
            ]
        }

    def process(self, text, context={}):
        detection_results = self._detect_inappropriate_content(text)

        if (
            detection_results["is_inappropriate"] and
            detection_results["confidence"] >= self.config["confidence_threshold"]
        ):
            action = "BLOCK" if self.config["block_inappropriate_content"] else "WARN"
            return {
                "action": action,
                "reason": "Inappropriate content detected",
                "details": detection_results
            }

        return {"action": "ALLOW"}

    def _detect_inappropriate_content(self, text):
        detected_patterns = []
        max_confidence = 0
        primary_category = None

        for category, patterns in self.inappropriate_patterns.items():
            if not self.config["categories"].get(category):
                continue
                
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    confidence = self._calculate_confidence(category, match.group())
                    detected_patterns.append({
                        "category": category,
                        "pattern": pattern,
                        "match": match.group(),
                        "confidence": confidence
                    })
                    if confidence > max_confidence:
                        max_confidence = confidence
                        primary_category = category

        return {
            "is_inappropriate": max_confidence > 0,
            "confidence": max_confidence,
            "detected_patterns": detected_patterns,
            "primary_category": primary_category
        }

    def _calculate_confidence(self, category, match_text):
        """Calculate confidence based on category and specific match"""
        base_confidence = {
            "sexual_content": 0.9,
            "adult_content": 0.85,
            "inappropriate_requests": 0.8
        }.get(category, 0.7)
        
        # Boost confidence for explicit terms
        explicit_terms = ["porno", "porn", "sesso", "sex", "nudo", "nude", "xxx"]
        if any(term in match_text.lower() for term in explicit_terms):
            base_confidence = min(0.95, base_confidence + 0.1)
        
        return base_confidence
