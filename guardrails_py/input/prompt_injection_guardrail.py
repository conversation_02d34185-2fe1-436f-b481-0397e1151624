
import re

class PromptInjectionGuardrail:
    def __init__(self, config={}):
        self.config = {
            "block_suspicious_patterns": True,
            "confidence_threshold": 0.7,
            **config
        }
        self.initialize_patterns()

    def initialize_patterns(self):
        self.injection_patterns = {
            "system_prompt_override": [
                r"ignore\s+(?:all\s+)?(?:previous\s+)?(?:instructions?|prompts?|rules?)",
                r"forget\s+(?:everything|all\s+instructions?|your\s+role)",
            ],
            "role_manipulation": [
                r"you\s+are\s+now\s+(?:a|an)\s+\w+",
                r"act\s+as\s+(?:a|an)\s+\w+",
            ],
        }

    def process(self, text, context={}):
        detection_results = self._detect_injection_attempts(text)

        if (
            detection_results["is_injection_attempt"] and
            detection_results["confidence"] >= self.config["confidence_threshold"]
        ):
            action = "BLOCK" if self.config["block_suspicious_patterns"] else "WARN"
            return {
                "action": action,
                "reason": "Potential prompt injection detected",
                "details": detection_results
            }

        return {"action": "ALLOW"}

    def _detect_injection_attempts(self, text):
        detected_patterns = []
        max_confidence = 0
        primary_type = None

        for type, patterns in self.injection_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    confidence = self._calculate_pattern_confidence(type)
                    detected_patterns.append({"type": type, "pattern": pattern})
                    if confidence > max_confidence:
                        max_confidence = confidence
                        primary_type = type

        return {
            "is_injection_attempt": max_confidence > 0,
            "confidence": max_confidence,
            "detected_patterns": detected_patterns,
            "primary_type": primary_type
        }

    def _calculate_pattern_confidence(self, type):
        if type == "system_prompt_override":
            return 0.9
        if type == "role_manipulation":
            return 0.8
        return 0.5

