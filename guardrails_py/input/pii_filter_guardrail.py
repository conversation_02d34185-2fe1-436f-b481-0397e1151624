
import re
from typing import List, Dict, Tuple

class PIIFilterGuardrail:
    def __init__(self, config={}):
        self.config = {
            "anonymization_strategy": "anonymize",  # or "block"
            "replacements": {
                "EMAIL_ADDRESS": "[EMAIL_REDACTED]",
                "PHONE_NUMBER": "[PHONE_REDACTED]",
                "CREDIT_CARD": "[CARD_REDACTED]",
                "ITALIAN_FISCAL_CODE": "[CF_REDACTED]",
                "ITALIAN_VAT": "[VAT_REDACTED]",
                "IBAN": "[IBAN_REDACTED]",
                "IP_ADDRESS": "[IP_REDACTED]",
                "DEFAULT": "[REDACTED]"
            },
            **config
        }
        self._initialize_patterns()

    def _initialize_patterns(self):
        """Initialize regex patterns for PII detection"""
        self.pii_patterns = {
            "EMAIL_ADDRESS": [
                r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            ],
            "PHONE_NUMBER": [
                # Italian phone numbers
                r'\b(?:\+39\s?)?(?:0\d{1,4}\s?)?\d{6,10}\b',
                r'\b(?:\+39[-.\s]?)?(?:\(0\d{1,4}\)[-.\s]?)?\d{6,10}\b',
                # International formats
                r'\b\+\d{1,3}[-.\s]?\d{1,14}\b',
                # Generic phone patterns
                r'\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b'
            ],
            "CREDIT_CARD": [
                # Visa, MasterCard, American Express, Discover
                r'\b(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|3[47][0-9]{13}|3[0-9]{13}|6(?:011|5[0-9]{2})[0-9]{12})\b',
                # Generic credit card pattern (groups of 4 digits)
                r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b'
            ],
            "ITALIAN_FISCAL_CODE": [
                # Italian Codice Fiscale pattern
                r'\b[A-Z]{6}\d{2}[A-Z]\d{2}[A-Z]\d{3}[A-Z]\b'
            ],
            "ITALIAN_VAT": [
                # Italian VAT number (Partita IVA)
                r'\bIT\d{11}\b',
                r'\b\d{11}\b(?=.*(?:partita|iva|p\.iva))'
            ],
            "IBAN": [
                # International Bank Account Number
                r'\b[A-Z]{2}\d{2}[A-Z0-9]{4}\d{7}[A-Z0-9]{0,16}\b'
            ],
            "IP_ADDRESS": [
                # IPv4 addresses
                r'\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b',
                # IPv6 addresses (simplified)
                r'\b(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}\b'
            ]
        }

    def process(self, text, context={}):
        pii_findings = self._detect_pii(text)

        if not pii_findings:
            return {"action": "ALLOW"}

        if self.config["anonymization_strategy"] == "block":
            return {
                "action": "BLOCK",
                "reason": "PII detected",
                "details": {"pii_found": pii_findings}
            }

        # Default to anonymize
        anonymized_text = self._anonymize_text(text, pii_findings)

        return {
            "action": "MODIFY",
            "modified_text": anonymized_text,
            "warning": "PII was detected and anonymized.",
            "details": {"pii_found": pii_findings}
        }

    def _detect_pii(self, text: str) -> List[Dict]:
        """Detect PII in text using regex patterns"""
        findings = []

        for pii_type, patterns in self.pii_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    findings.append({
                        "entity_type": pii_type,
                        "start": match.start(),
                        "end": match.end(),
                        "text": match.group(),
                        "confidence": 0.9  # High confidence for regex matches
                    })

        # Sort by start position to handle overlapping matches
        findings.sort(key=lambda x: x["start"])

        # Remove overlapping matches (keep the first one)
        filtered_findings = []
        last_end = -1
        for finding in findings:
            if finding["start"] >= last_end:
                filtered_findings.append(finding)
                last_end = finding["end"]

        return filtered_findings

    def _anonymize_text(self, text: str, findings: List[Dict]) -> str:
        """Replace PII in text with anonymized versions"""
        if not findings:
            return text

        # Sort findings by start position in reverse order to maintain indices
        findings_sorted = sorted(findings, key=lambda x: x["start"], reverse=True)

        anonymized_text = text
        for finding in findings_sorted:
            replacement = self.config["replacements"].get(
                finding["entity_type"],
                self.config["replacements"]["DEFAULT"]
            )
            anonymized_text = (
                anonymized_text[:finding["start"]] +
                replacement +
                anonymized_text[finding["end"]:]
            )

        return anonymized_text

