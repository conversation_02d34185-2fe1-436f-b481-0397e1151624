
import re
import random

class HarmfulContentFilterGuardrail:
    def __init__(self, config={}):
        self.config = {
            "confidence_threshold": 0.7,
            "enable_replacement": True,
            "categories": {
                "violence": True,
                "hate_speech": True,
                "self_harm": True,
                "sexual_content": True,
                "inappropriate_responses": True,
            },
            **config
        }
        self.initialize_patterns()
        self.initialize_replacements()

    def initialize_patterns(self):
        self.patterns = {
            "violence": [
                r"\b(kill|murder|assassinate|execute|slaughter)\s+(you|him|her|them|someone)\b",
                r"\b(hurt|harm|injure|attack|assault|beat)\s+(you|someone|people)\b",
            ],
            "hate_speech": [
                r"\b(hate|despise|loathe)\s+(all|every|those)\s+(people|persons|individuals)\b",
            ],
            "self_harm": [
                r"\b(kill|hurt|harm)\s+yourself\b",
                r"\b(suicide|self-harm|self-injury)\s+(is|would\s+be|might\s+be)\s+(good|better|solution)\b",
            ],
            "sexual_content": [
                # Italian sexual content patterns
                r"\b(porno|pornografia|pornografico|pornografici|pornografiche)\b",
                r"\b(film\s+porno|video\s+porno|materiale\s+pornografico)\b",
                r"\b(contenuti?\s+sessuali?|contenuti?\s+per\s+adulti)\b",
                r"\b(sesso|sessuale|sessuali|erotici?|erotica|erotiche)\b",
                r"\b(nud[oi]|nuda|nude|nudi)\b",
                r"\b(masturbazione|masturbare|orgasmo)\b",
                r"\b(genitali|pene|vagina|testicoli)\b",
                r"\b(prostituzione|prostituta|escort|bordello)\b",

                # English sexual content patterns
                r"\b(porn|pornography|pornographic|adult\s+videos?)\b",
                r"\b(sexual\s+content|adult\s+content|erotic\s+content)\b",
                r"\b(sex|sexual|erotic|nude|naked)\b",
                r"\b(masturbation|orgasm|genitals)\b",
                r"\b(penis|vagina|testicles|clitoris)\b",
                r"\b(prostitution|prostitute|brothel)\b",

                # References to adult sites/content
                r"\b(pornhub|xvideos|redtube|onlyfans)\b",
                r"\b(xxx|nsfw|r18|18\+)\b",

                # Phrases that mention sexual content
                r"\b(natura\s+pornografica|carattere\s+sessuale)\b",
                r"\b(sexual\s+nature|pornographic\s+nature)\b",
            ],
            "inappropriate_responses": [
                # Responses that acknowledge or discuss inappropriate requests
                r"\b(non\s+trovo\s+.*\s+porno|non\s+.*\s+film\s+porno)\b",
                r"\b(non\s+.*\s+materiale\s+pornografico)\b",
                r"\b(cannot\s+find\s+.*\s+porn|no\s+.*\s+pornographic)\b",
                r"\b(don['\']t\s+have\s+.*\s+sexual\s+content)\b",

                # Responses that repeat inappropriate terms
                r"\b(relativa\s+a\s+film\s+porno|related\s+to\s+porn)\b",
                r"\b(riferimento\s+a\s+materiale\s+.*\s+pornografic)\b",
            ]
        }

    def initialize_replacements(self):
        self.replacements = {
            "violence": ["I cannot provide information that could cause physical harm."],
            "hate_speech": ["I do not support hate speech or discrimination."],
            "self_harm": ["If you are going through a difficult time, I recommend talking to a qualified professional."],
            "sexual_content": [
                "I cannot provide or discuss sexual or adult content.",
                "I'm designed to provide helpful information on appropriate topics only.",
                "I cannot assist with requests for adult or sexual content."
            ],
            "inappropriate_responses": [
                "I can only provide information about appropriate topics related to the documentation.",
                "I'm here to help with technical questions about the product documentation.",
                "I can assist you with questions about the product features and technical specifications."
            ],
            "default": ["This content is not appropriate for our conversation."]
        }

    def process(self, text, context={}):
        analysis_result = self._analyze_content(text)

        if analysis_result["is_harmful"] and analysis_result["confidence"] >= self.config["confidence_threshold"]:
            replacement_text = self._generate_safe_replacement(analysis_result["category"])
            return {
                "action": "MODIFY",
                "modified_text": replacement_text,
                "warning": "The content has been modified to ensure safety.",
                "details": analysis_result
            }

        return {"action": "ALLOW"}

    def _analyze_content(self, text):
        max_confidence = 0
        primary_category = None

        for category, patterns in self.patterns.items():
            if not self.config["categories"].get(category):
                continue
            for pattern in patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    confidence = self._calculate_harm_confidence(category)
                    if confidence > max_confidence:
                        max_confidence = confidence
                        primary_category = category
        
        return {
            "is_harmful": max_confidence > 0,
            "confidence": max_confidence,
            "category": primary_category
        }

    def _calculate_harm_confidence(self, category):
        if category in ["violence", "self_harm", "hate_speech"]:
            return 0.9
        elif category in ["sexual_content", "inappropriate_responses"]:
            return 0.85
        return 0.7

    def _generate_safe_replacement(self, category):
        options = self.replacements.get(category, self.replacements["default"])
        return random.choice(options)

