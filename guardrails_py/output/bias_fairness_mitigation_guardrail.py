
import re
import random

class BiasFairnessMitigationGuardrail:
    def __init__(self, config={}):
        self.config = {
            "confidence_threshold": 0.6,
            "auto_correct": True,
            "enable_gender_bias_detection": True,
            **config
        }
        self.initialize_bias_patterns()
        self.initialize_corrections()

    def initialize_bias_patterns(self):
        self.gender_bias_patterns = {
            "professional_stereotypes": [
                r"\b(male|man|men)\s+(doctor|engineer|pilot)",
                r"\b(female|woman|women)\s+(nurse|teacher|secretary)",
            ],
            "exclusive_language": [
                r"\bguys\b",
                r"\bmankind\b",
            ],
        }

    def initialize_corrections(self):
        self.corrections = {
            "inclusive_language": {
                "guys": "everyone",
                "mankind": "humanity",
            },
            "neutral_phrases": {
                "he or she": "they",
            }
        }

    def process(self, text, context={}):
        bias_analysis = self._analyze_bias(text)

        if bias_analysis["has_bias"] and bias_analysis["confidence"] >= self.config["confidence_threshold"]:
            corrected_text = self._correct_bias(text) if self.config["auto_correct"] else text
            action = "MODIFY" if self.config["auto_correct"] else "WARN"

            return {
                "action": action,
                "modified_text": corrected_text if action == "MODIFY" else None,
                "warning": "The content has been reviewed for fairness and inclusivity.",
                "details": bias_analysis
            }

        return {"action": "ALLOW"}

    def _analyze_bias(self, text):
        max_confidence = 0
        primary_bias_type = None

        if self.config["enable_gender_bias_detection"]:
            for category, patterns in self.gender_bias_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, text, re.IGNORECASE):
                        confidence = 0.7  # Simplified confidence
                        if confidence > max_confidence:
                            max_confidence = confidence
                            primary_bias_type = "gender"

        return {
            "has_bias": max_confidence > 0,
            "confidence": max_confidence,
            "primary_bias_type": primary_bias_type
        }

    def _correct_bias(self, text):
        corrected_text = text
        for biased, inclusive in self.corrections["inclusive_language"].items():
            corrected_text = re.sub(r"\b" + biased + r"\b", inclusive, corrected_text, flags=re.IGNORECASE)
        return corrected_text

