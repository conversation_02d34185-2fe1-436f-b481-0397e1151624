
import time
import logging
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

class GuardrailManager:
    def __init__(self, input_guardrails=None, output_guardrails=None, config={}):
        self.config = {
            "enable_input_guardrails": True,
            "enable_output_guardrails": True,
            "log_level": "info",
            "parallel_execution": True,
            "max_workers": 3,
            "guardrail_timeout": 5.0,
            **config
        }
        
        self.input_guardrails = input_guardrails or []
        self.output_guardrails = output_guardrails or []
        self.audit_log = []
        
        self.log("info", "GuardrailManager initialized", {
            "input_guardrails": len(self.input_guardrails),
            "output_guardrails": len(self.output_guardrails),
            "parallel_execution": self.config["parallel_execution"]
        })

    def _execute_guardrails_parallel(self, guardrails, text):
        """Esegue i guardrails in parallelo per migliorare le performance."""
        if not guardrails or not self.config["parallel_execution"]:
            return self._execute_guardrails_sequential(guardrails, text)

        results = []

        def run_guardrail(guardrail):
            try:
                return guardrail.process(text)
            except Exception as e:
                self.log("error", f"Guardrail {guardrail.__class__.__name__} failed", {"error": str(e)})
                return {"action": "ALLOW", "error": str(e)}

        with ThreadPoolExecutor(max_workers=self.config["max_workers"]) as executor:
            # Sottometti tutti i guardrails
            future_to_guardrail = {
                executor.submit(run_guardrail, guardrail): guardrail
                for guardrail in guardrails
            }

            # Raccogli i risultati
            for future in as_completed(future_to_guardrail, timeout=self.config["guardrail_timeout"]):
                guardrail = future_to_guardrail[future]
                try:
                    result = future.result()
                    results.append((guardrail, result))

                    # Early exit se un guardrail blocca
                    if result.get("action") == "BLOCK":
                        # Cancella i future rimanenti
                        for remaining_future in future_to_guardrail:
                            if remaining_future != future:
                                remaining_future.cancel()
                        break

                except Exception as e:
                    self.log("error", f"Guardrail {guardrail.__class__.__name__} exception", {"error": str(e)})
                    results.append((guardrail, {"action": "ALLOW", "error": str(e)}))

        return results

    def _execute_guardrails_sequential(self, guardrails, text):
        """Esegue i guardrails sequenzialmente (fallback)."""
        results = []
        for guardrail in guardrails:
            try:
                result = guardrail.process(text)
                results.append((guardrail, result))

                # Early exit se un guardrail blocca
                if result.get("action") == "BLOCK":
                    break

            except Exception as e:
                self.log("error", f"Guardrail {guardrail.__class__.__name__} failed", {"error": str(e)})
                results.append((guardrail, {"action": "ALLOW", "error": str(e)}))

        return results

    def process_input(self, user_input, context={}):
        start_time = time.time()
        session_id = context.get("session_id", "unknown")
        
        processed_input = {
            "text": user_input,
            "original_text": user_input,
            "metadata": {
                "session_id": session_id,
                "timestamp": datetime.utcnow().isoformat(),
                "guardrail_results": []
            },
            "blocked": False,
            "modified": False,
            "warnings": []
        }
        
        self.log("info", "Starting input processing", {
            "session_id": session_id,
            "input_length": len(user_input),
            "guardrail_count": len(self.input_guardrails)
        })
        
        # Esegui i guardrails (parallelo o sequenziale)
        guardrail_results = self._execute_guardrails_parallel(self.input_guardrails, processed_input["text"])

        for guardrail, result in guardrail_results:
            processed_input["metadata"]["guardrail_results"].append(result)

            if result.get("action") == "BLOCK":
                processed_input["blocked"] = True
                processed_input["reason"] = result.get("reason", "Input blocked by guardrail")
                self.log("warn", "Input blocked", {"session_id": session_id, "guardrail": guardrail.__class__.__name__})
                break  # Stop processing further guardrails

            if result.get("action") == "MODIFY":
                processed_input["text"] = result.get("modified_text", processed_input["text"])
                processed_input["modified"] = True
                self.log("info", "Input modified", {"session_id": session_id, "guardrail": guardrail.__class__.__name__})

            if result.get("warning"):
                processed_input["warnings"].append(result["warning"])
                self.log("warn", "Input warning", {"session_id": session_id, "guardrail": guardrail.__class__.__name__, "warning": result["warning"]})


        total_time = time.time() - start_time
        processed_input["metadata"]["total_processing_time"] = total_time
        
        self.log("info", "Input processing completed", {
            "session_id": session_id,
            "total_time": total_time,
        })
        
        return processed_input

    def process_output(self, ai_output, context={}):
        start_time = time.time()
        session_id = context.get("session_id", "unknown")

        processed_output = {
            "text": ai_output,
            "original_text": ai_output,
            "metadata": {
                "session_id": session_id,
                "timestamp": datetime.utcnow().isoformat(),
                "guardrail_results": []
            },
            "blocked": False,
            "modified": False,
            "warnings": []
        }

        self.log("info", "Starting output processing", {
            "session_id": session_id,
            "output_length": len(ai_output),
            "guardrail_count": len(self.output_guardrails)
        })

        # Esegui i guardrails (parallelo o sequenziale)
        guardrail_results = self._execute_guardrails_parallel(self.output_guardrails, processed_output["text"])

        for guardrail, result in guardrail_results:
            processed_output["metadata"]["guardrail_results"].append(result)

            if result.get("action") == "BLOCK":
                processed_output["blocked"] = True
                processed_output["reason"] = result.get("reason", "Output blocked by guardrail")
                self.log("warn", "Output blocked", {"session_id": session_id, "guardrail": guardrail.__class__.__name__})
                break

            if result.get("action") == "MODIFY":
                processed_output["text"] = result.get("modified_text", processed_output["text"])
                processed_output["modified"] = True
                self.log("info", "Output modified", {"session_id": session_id, "guardrail": guardrail.__class__.__name__})

            if result.get("warning"):
                processed_output["warnings"].append(result["warning"])
                self.log("warn", "Output warning", {"session_id": session_id, "guardrail": guardrail.__class__.__name__, "warning": result["warning"]})


        total_time = time.time() - start_time
        processed_output["metadata"]["total_processing_time"] = total_time

        self.log("info", "Output processing completed", {
            "session_id": session_id,
            "total_time": total_time,
        })

        return processed_output

    def get_statistics(self):
        return {
            "total_processed": len(self.audit_log),
            "input_guardrails": len(self.input_guardrails),
            "output_guardrails": len(self.output_guardrails),
            "recent_activity": self.audit_log[-100:]
        }

    def log(self, level, message, data={}):
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": level,
            "message": message,
            "data": data
        }
        self.audit_log.append(log_entry)
        
        if len(self.audit_log) > 1000:
            self.audit_log.pop(0)
            
        # Basic console logging
        logging.basicConfig(level=self.config.get("log_level", "INFO").upper())
        logger = logging.getLogger(__name__)
        getattr(logger, level.lower(), logger.info)(f"{message} {data}")

