#!/usr/bin/env python3
"""
Test script for the conversation logging system.
This script tests the integration between the chatbot and the logging system.
"""

import os
import sys
import json
import time
import requests
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_database_connection():
    """Test database connection."""
    print("🔍 Testing database connection...")
    try:
        from database import db_manager
        
        if db_manager.test_connection():
            print("✅ Database connection successful")
            return True
        else:
            print("❌ Database connection failed")
            return False
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False

def test_logging_service():
    """Test the logging service."""
    print("🔍 Testing logging service...")
    try:
        from logging_service import conversation_logger
        
        # Test sample conversation logging
        sample_conversation = {
            'ip_address': '127.0.0.1',
            'product': 'TEST_PRODUCT',
            'question': 'Test question for logging system',
            'response': 'Test response from logging system',
            'guardrail_log': [
                {"action": "ALLOW", "details": {"metrics": {"characters": 32, "words": 5}}},
                {"action": "ALLOW", "details": {"detected_language": "it", "confidence": 0.5714285714285714}},
                {"action": "ALLOW"},
                {"action": "ALLOW"},
                {"action": "ALLOW"}
            ],
            'session_id': 'test_session_123',
            'user_agent': 'Test User Agent',
            'response_time_ms': 150,
            'confidence_score': 0.95
        }
        
        # Log asynchronously
        success = conversation_logger.log_conversation_async(sample_conversation)
        
        if success:
            print("✅ Conversation queued for logging successfully")
            
            # Wait a bit for async processing
            time.sleep(2)
            
            # Check queue status
            status = conversation_logger.get_queue_status()
            print(f"📊 Queue status: {status}")
            
            return True
        else:
            print("❌ Failed to queue conversation for logging")
            return False
            
    except Exception as e:
        print(f"❌ Logging service error: {e}")
        return False

def test_flask_app():
    """Test the Flask application with logging."""
    print("🔍 Testing Flask application...")
    
    # Start the Flask app in a separate process (this is just a simulation)
    # In a real test, you would use a test client or start the app separately
    
    base_url = "http://localhost:5001"
    
    # Test health endpoint
    try:
        response = requests.get(f"{base_url}/api/conversations/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Health check passed: {health_data}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"⚠️ Flask app not running or not accessible: {e}")
        print("💡 Start the Flask app with 'python app.py' to test the full integration")
        return False

def test_api_endpoints():
    """Test the conversation API endpoints."""
    print("🔍 Testing API endpoints...")
    
    base_url = "http://localhost:5001"
    
    endpoints_to_test = [
        "/api/conversations/stats",
        "/api/conversations/search",
        "/api/conversations/daily-stats",
        "/api/conversations/products",
        "/api/conversations/health"
    ]
    
    results = {}
    
    for endpoint in endpoints_to_test:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            results[endpoint] = {
                'status_code': response.status_code,
                'success': response.status_code == 200
            }
            
            if response.status_code == 200:
                print(f"✅ {endpoint} - OK")
            else:
                print(f"❌ {endpoint} - Status: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            results[endpoint] = {
                'error': str(e),
                'success': False
            }
            print(f"⚠️ {endpoint} - Error: {e}")
    
    successful_endpoints = sum(1 for r in results.values() if r.get('success', False))
    total_endpoints = len(endpoints_to_test)
    
    print(f"📊 API Endpoints Test: {successful_endpoints}/{total_endpoints} successful")
    
    return successful_endpoints > 0

def test_guardrail_data_structure():
    """Test that guardrail data structure matches expected format."""
    print("🔍 Testing guardrail data structure...")

    # Expected structure based on your example - now only guardrail_results
    expected_structure = [
        {"action": "ALLOW", "details": {"metrics": {"characters": 32, "words": 5}}},
        {"action": "ALLOW", "details": {"detected_language": "it", "confidence": 0.5714285714285714}},
        {"action": "ALLOW"},
        {"action": "ALLOW"},
        {"action": "ALLOW"}
    ]

    print("✅ Expected guardrail data structure (only guardrail_results):")
    print(json.dumps(expected_structure, indent=2))

    return True

def main():
    """Main test function."""
    print("🚀 Starting conversation logging system tests...")
    print("=" * 60)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Logging Service", test_logging_service),
        ("Guardrail Data Structure", test_guardrail_data_structure),
        ("Flask Application", test_flask_app),
        ("API Endpoints", test_api_endpoints),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 Running test: {test_name}")
        print("-" * 40)
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {e}")
            results[test_name] = False
        
        print("-" * 40)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if result:
            passed_tests += 1
    
    print("-" * 60)
    print(f"Total: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! The logging system is ready to use.")
    elif passed_tests > 0:
        print("⚠️ Some tests passed. Check the failed tests above.")
    else:
        print("❌ All tests failed. Please check your configuration.")
    
    print("\n💡 Next steps:")
    print("1. Run 'python setup_logging.py' to initialize the database")
    print("2. Start the Flask app with 'python app.py'")
    print("3. Test the chat functionality and check the database for logged conversations")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
