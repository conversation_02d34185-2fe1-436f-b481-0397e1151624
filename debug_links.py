#!/usr/bin/env python3
"""
Script di debug per verificare la generazione e gestione dei link ai PDF.
"""

import os
import sys
from pathlib import Path

# Carica le variabili d'ambiente (se disponibili)
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️ dotenv non disponibile, usando variabili d'ambiente di sistema")

def test_link_generation():
    """Testa la generazione dei link ai PDF."""
    print("🔗 Test Generazione Link")
    print("=" * 40)
    
    try:
        from pdf_chatbot_prodotti import ProductChatbot
        
        # Inizializza il chatbot
        jina_api_key = os.getenv('JINA_API_KEY')
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        
        if not jina_api_key or not gemini_api_key:
            print("❌ API keys non trovate")
            return False
        
        chatbot = ProductChatbot(jina_api_key, gemini_api_key)
        
        # Test creazione link
        test_file_path = "pdf/ProdottoA/1.pdf"
        test_page = 1
        
        link = chatbot._create_file_link(test_file_path, test_page)
        print(f"✓ Link generato: {link}")
        
        # Test formato markdown
        source_ref = f"[📄 {Path(test_file_path).name} - Pag. {test_page}]({link})"
        print(f"✓ Riferimento markdown: {source_ref}")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore: {e}")
        return False

def test_markdown_conversion():
    """Testa la conversione markdown dei link."""
    print("\n📝 Test Conversione Markdown")
    print("=" * 40)
    
    try:
        import markdown2
        
        # Test link markdown
        test_markdown = "Ecco il documento [📄 1.pdf - Pag. 1](/pdf/ProdottoA/1.pdf#page=1) con le informazioni."
        
        html_result = markdown2.markdown(test_markdown)
        print(f"✓ Markdown input: {test_markdown}")
        print(f"✓ HTML output: {html_result}")
        
        # Verifica che il link sia presente
        if '<a href="/pdf/ProdottoA/1.pdf#page=1">' in html_result:
            print("✅ Link convertito correttamente")
            return True
        else:
            print("❌ Link non convertito correttamente")
            return False
            
    except Exception as e:
        print(f"❌ Errore: {e}")
        return False

def test_ai_cleaning():
    """Testa il sistema di pulizia delle risposte AI."""
    print("\n🧹 Test Pulizia Risposte AI")
    print("=" * 40)
    
    try:
        from pdf_chatbot_prodotti import ProductChatbot
        
        # Inizializza il chatbot
        jina_api_key = os.getenv('JINA_API_KEY')
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        
        if not jina_api_key or not gemini_api_key:
            print("❌ API keys non trovate")
            return False
        
        chatbot = ProductChatbot(jina_api_key, gemini_api_key)
        
        # Test risposta con link
        test_response = """In base alla documentazione, puoi trovare le informazioni nel documento [📄 1.pdf - Pag. 1](/pdf/ProdottoA/1.pdf#page=1) e anche in [📄 2.pdf - Pag. 3](/pdf/ProdottoA/2.pdf#page=3). Gentile utente, spero che questo aiuti."""
        
        cleaned = chatbot._clean_ai_references(test_response)
        
        print(f"✓ Risposta originale: {test_response}")
        print(f"✓ Risposta pulita: {cleaned}")
        
        # Verifica che i link siano preservati
        if "[📄 1.pdf - Pag. 1](/pdf/ProdottoA/1.pdf#page=1)" in cleaned:
            print("✅ Link preservati correttamente")
            return True
        else:
            print("❌ Link rimossi durante la pulizia")
            return False
            
    except Exception as e:
        print(f"❌ Errore: {e}")
        return False

def test_prompt_instructions():
    """Verifica le istruzioni nel prompt riguardo ai link."""
    print("\n📋 Test Istruzioni Prompt")
    print("=" * 40)
    
    # Simula la generazione del prompt
    test_context = """[📄 1.pdf - Pag. 1](/pdf/ProdottoA/1.pdf#page=1)
Contenuto del documento di esempio..."""
    
    test_query = "Come funziona il sistema?"
    
    # Verifica che le istruzioni sui link siano presenti
    prompt_instructions = """
REGOLE:
- Rispondi in italiano, tono cordiale
- Mantieni SEMPRE i link [📄 ...] nelle citazioni
- Non usare frasi come "in base ai documenti" o "gentile utente"
- Inizia direttamente con l'informazione
- Se non hai info: "Non ho informazioni specifiche su questo"
"""
    
    print(f"✓ Contesto con link: {test_context[:100]}...")
    print(f"✓ Istruzioni sui link presenti: {'SEMPRE i link' in prompt_instructions}")
    
    return True

def main():
    """Funzione principale di debug."""
    print("🔍 Debug Link ai PDF - Chat-Jina")
    print("=" * 50)
    
    tests = [
        ("Generazione Link", test_link_generation),
        ("Conversione Markdown", test_markdown_conversion),
        ("Pulizia AI", test_ai_cleaning),
        ("Istruzioni Prompt", test_prompt_instructions),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Errore in {test_name}: {e}")
            results[test_name] = False
    
    # Riepilogo
    print("\n" + "=" * 50)
    print("📊 RIEPILOGO DEBUG")
    print("=" * 50)
    
    for test_name, result in results.items():
        status = "✅ OK" if result else "❌ PROBLEMA"
        print(f"{status} {test_name}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    if passed == total:
        print(f"\n🎉 Tutti i test OK ({passed}/{total})")
        print("I link dovrebbero funzionare correttamente.")
    else:
        print(f"\n⚠️ Problemi rilevati ({passed}/{total} OK)")
        print("Controlla i test falliti per identificare il problema.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
