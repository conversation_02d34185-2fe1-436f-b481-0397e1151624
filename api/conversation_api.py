"""
API endpoints for conversation statistics and queries.
"""

import logging
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
from typing import Dict, Any, Optional
from database import db_manager
from logging_service import conversation_logger

logger = logging.getLogger(__name__)

# Create blueprint for conversation API
conversation_bp = Blueprint('conversation_api', __name__, url_prefix='/api/conversations')


@conversation_bp.route('/stats', methods=['GET'])
def get_conversation_stats():
    """
    Get conversation statistics.
    
    Query parameters:
    - date_from: Start date (YYYY-MM-DD)
    - date_to: End date (YYYY-MM-DD)
    - product: Filter by product code
    - ip_address: Filter by IP address
    """
    try:
        # Parse query parameters
        filters = {}
        
        date_from = request.args.get('date_from')
        if date_from:
            try:
                filters['date_from'] = datetime.strptime(date_from, '%Y-%m-%d')
            except ValueError:
                return jsonify({'error': 'Invalid date_from format. Use YYYY-MM-DD'}), 400
        
        date_to = request.args.get('date_to')
        if date_to:
            try:
                filters['date_to'] = datetime.strptime(date_to, '%Y-%m-%d')
            except ValueError:
                return jsonify({'error': 'Invalid date_to format. Use YYYY-MM-DD'}), 400
        
        product = request.args.get('product')
        if product:
            filters['product'] = product
        
        ip_address = request.args.get('ip_address')
        if ip_address:
            filters['ip_address'] = ip_address
        
        # Get statistics from database
        stats = db_manager.get_conversation_stats(filters)
        
        # Add queue status
        queue_status = conversation_logger.get_queue_status()
        stats['queue_status'] = queue_status
        
        return jsonify({
            'success': True,
            'data': stats,
            'filters_applied': filters
        })
        
    except Exception as e:
        logger.error(f"Error getting conversation stats: {e}")
        return jsonify({'error': 'Internal server error'}), 500


@conversation_bp.route('/search', methods=['GET'])
def search_conversations():
    """
    Search conversations with filtering and pagination.
    
    Query parameters:
    - date_from: Start date (YYYY-MM-DD)
    - date_to: End date (YYYY-MM-DD)
    - product: Filter by product code
    - ip_address: Filter by IP address
    - session_id: Filter by session ID
    - limit: Number of results (default: 50, max: 500)
    - offset: Offset for pagination (default: 0)
    """
    try:
        # Parse query parameters
        filters = {}
        
        date_from = request.args.get('date_from')
        if date_from:
            try:
                filters['date_from'] = datetime.strptime(date_from, '%Y-%m-%d')
            except ValueError:
                return jsonify({'error': 'Invalid date_from format. Use YYYY-MM-DD'}), 400
        
        date_to = request.args.get('date_to')
        if date_to:
            try:
                filters['date_to'] = datetime.strptime(date_to, '%Y-%m-%d')
            except ValueError:
                return jsonify({'error': 'Invalid date_to format. Use YYYY-MM-DD'}), 400
        
        product = request.args.get('product')
        if product:
            filters['product'] = product
        
        ip_address = request.args.get('ip_address')
        if ip_address:
            filters['ip_address'] = ip_address
        
        session_id = request.args.get('session_id')
        if session_id:
            filters['session_id'] = session_id
        
        # Parse pagination parameters
        try:
            limit = min(int(request.args.get('limit', 50)), 500)  # Max 500 results
            offset = int(request.args.get('offset', 0))
        except ValueError:
            return jsonify({'error': 'Invalid limit or offset parameter'}), 400
        
        # Get conversations from database
        conversations = db_manager.get_conversations(filters, limit, offset)
        
        # Convert datetime objects to strings for JSON serialization
        for conv in conversations:
            if conv.get('timestamp'):
                conv['timestamp'] = conv['timestamp'].isoformat()
        
        return jsonify({
            'success': True,
            'data': conversations,
            'count': len(conversations),
            'limit': limit,
            'offset': offset,
            'filters_applied': filters
        })
        
    except Exception as e:
        logger.error(f"Error searching conversations: {e}")
        return jsonify({'error': 'Internal server error'}), 500


@conversation_bp.route('/daily-stats', methods=['GET'])
def get_daily_stats():
    """
    Get daily conversation statistics for the last 30 days.
    
    Query parameters:
    - days: Number of days to include (default: 30, max: 365)
    - product: Filter by product code
    """
    try:
        # Parse parameters
        try:
            days = min(int(request.args.get('days', 30)), 365)
        except ValueError:
            return jsonify({'error': 'Invalid days parameter'}), 400
        
        product = request.args.get('product')
        
        # Calculate date range
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days-1)
        
        # Build filters
        filters = {
            'date_from': start_date,
            'date_to': end_date
        }
        
        if product:
            filters['product'] = product
        
        # Get conversations for the period
        conversations = db_manager.get_conversations(filters, limit=10000)  # Large limit to get all
        
        # Group by date
        daily_stats = {}
        for conv in conversations:
            date_key = conv['timestamp'].date().isoformat()
            if date_key not in daily_stats:
                daily_stats[date_key] = {
                    'date': date_key,
                    'total_conversations': 0,
                    'unique_users': set(),
                    'unique_sessions': set(),
                    'avg_response_time': 0,
                    'total_response_time': 0
                }
            
            daily_stats[date_key]['total_conversations'] += 1
            daily_stats[date_key]['unique_users'].add(conv['ip_address'])
            daily_stats[date_key]['unique_sessions'].add(conv['session_id'])
            
            if conv['response_time_ms']:
                daily_stats[date_key]['total_response_time'] += conv['response_time_ms']
        
        # Convert sets to counts and calculate averages
        result = []
        for date_key, stats in daily_stats.items():
            stats['unique_users'] = len(stats['unique_users'])
            stats['unique_sessions'] = len(stats['unique_sessions'])
            
            if stats['total_conversations'] > 0:
                stats['avg_response_time'] = stats['total_response_time'] / stats['total_conversations']
            
            del stats['total_response_time']  # Remove intermediate field
            result.append(stats)
        
        # Sort by date
        result.sort(key=lambda x: x['date'])
        
        return jsonify({
            'success': True,
            'data': result,
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'days': days
            },
            'filters_applied': {'product': product} if product else {}
        })
        
    except Exception as e:
        logger.error(f"Error getting daily stats: {e}")
        return jsonify({'error': 'Internal server error'}), 500


@conversation_bp.route('/products', methods=['GET'])
def get_products():
    """Get list of products with conversation counts."""
    try:
        # Get all conversations grouped by product
        conversations = db_manager.get_conversations(limit=10000)  # Large limit to get all
        
        # Group by product
        product_stats = {}
        for conv in conversations:
            product = conv.get('product', 'unknown')
            if product not in product_stats:
                product_stats[product] = {
                    'product': product,
                    'total_conversations': 0,
                    'unique_users': set(),
                    'last_conversation': None
                }
            
            product_stats[product]['total_conversations'] += 1
            product_stats[product]['unique_users'].add(conv['ip_address'])
            
            # Track most recent conversation
            if (product_stats[product]['last_conversation'] is None or 
                conv['timestamp'] > product_stats[product]['last_conversation']):
                product_stats[product]['last_conversation'] = conv['timestamp']
        
        # Convert to list and format
        result = []
        for product, stats in product_stats.items():
            stats['unique_users'] = len(stats['unique_users'])
            if stats['last_conversation']:
                stats['last_conversation'] = stats['last_conversation'].isoformat()
            result.append(stats)
        
        # Sort by total conversations (descending)
        result.sort(key=lambda x: x['total_conversations'], reverse=True)
        
        return jsonify({
            'success': True,
            'data': result,
            'total_products': len(result)
        })
        
    except Exception as e:
        logger.error(f"Error getting products: {e}")
        return jsonify({'error': 'Internal server error'}), 500


@conversation_bp.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for the conversation logging system."""
    try:
        # Test database connection
        db_healthy = db_manager.test_connection()
        
        # Get queue status
        queue_status = conversation_logger.get_queue_status()
        
        # Determine overall health
        healthy = db_healthy and queue_status['main_queue_size'] < 500  # Arbitrary threshold
        
        return jsonify({
            'success': True,
            'healthy': healthy,
            'database_connected': db_healthy,
            'queue_status': queue_status,
            'timestamp': datetime.utcnow().isoformat()
        }), 200 if healthy else 503
        
    except Exception as e:
        logger.error(f"Error in health check: {e}")
        return jsonify({
            'success': False,
            'healthy': False,
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 503


def register_conversation_api(app):
    """Register conversation API blueprint with Flask app."""
    app.register_blueprint(conversation_bp)
    logger.info("Conversation API registered")
