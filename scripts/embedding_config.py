#!/usr/bin/env python3
"""
Configurazioni per il sistema di embedding automatizzato
"""

import os
from pathlib import Path
from typing import List, Dict, Any
from dotenv import load_dotenv

# Carica automaticamente il file .env
load_dotenv()

class EmbeddingConfig:
    """Configurazioni centrali per il sistema di embedding"""
    
    # Percorsi
    PDF_BASE_DIR = Path("pdf")
    CHROMADB_PATH = Path("./chromadb_data")
    METADATA_FILE = Path("pdf_metadata.json")
    LOGS_DIR = Path("logs")
    
    # Estensioni supportate
    SUPPORTED_EXTENSIONS = [".pdf", ".txt", ".md", ".csv", ".doc", ".docx"]
    
    # Configurazioni embedding
    CHUNK_SIZE = 1000
    CHUNK_OVERLAP = 200
    BATCH_SIZE = 50
    MAX_RETRIES = 3
    
    # Configurazioni API
    JINA_MODEL = "jina-embeddings-v4"
    JINA_API_URL = "https://api.jina.ai/v1/embeddings"
    MAX_TEXT_LENGTH = 8000
    
    # Configurazioni ChromaDB
    COLLECTION_NAME = "product_documents_v2"
    
    # Configurazioni logging
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE_MAX_SIZE = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5
    
    # Configurazioni progress
    PROGRESS_UPDATE_INTERVAL = 1  # secondi
    
    # Configurazioni scheduling
    DEFAULT_SCHEDULE_INTERVAL = 3600  # 1 ora in secondi
    SCHEDULE_CONFIG_FILE = Path("schedule_config.json")
    
    # Configurazioni performance
    SLEEP_BETWEEN_BATCHES = 1  # secondi
    CONCURRENT_WORKERS = 1  # Per ora manteniamo sequenziale
    
    # Configurazioni report
    REPORT_DIR = Path("reports")
    REPORT_TEMPLATE = "embedding_report_{timestamp}.json"
    
    @classmethod
    def get_api_keys(cls) -> Dict[str, str]:
        """Recupera le API keys dalle variabili d'ambiente"""
        return {
            'jina_api_key': os.getenv("JINA_API_KEY"),
            'gemini_api_key': os.getenv("GEMINI_API_KEY")
        }
    
    @classmethod
    def validate_config(cls) -> List[str]:
        """Valida la configurazione e restituisce eventuali errori"""
        errors = []
        
        # Verifica API keys
        api_keys = cls.get_api_keys()
        if not api_keys['jina_api_key']:
            errors.append("JINA_API_KEY non trovata nelle variabili d'ambiente")
        if not api_keys['gemini_api_key']:
            errors.append("GEMINI_API_KEY non trovata nelle variabili d'ambiente")
        
        # Verifica directory
        if not cls.PDF_BASE_DIR.exists():
            errors.append(f"Directory PDF non trovata: {cls.PDF_BASE_DIR}")
        
        # Crea directory necessarie
        cls.LOGS_DIR.mkdir(exist_ok=True)
        cls.REPORT_DIR.mkdir(exist_ok=True)
        
        return errors
    
    @classmethod
    def get_log_file_path(cls, script_name: str) -> Path:
        """Genera il percorso del file di log per uno script specifico"""
        return cls.LOGS_DIR / f"{script_name}.log"
    
    @classmethod
    def get_report_file_path(cls, timestamp: str) -> Path:
        """Genera il percorso del file di report"""
        filename = cls.REPORT_TEMPLATE.format(timestamp=timestamp)
        return cls.REPORT_DIR / filename

# Configurazioni specifiche per diversi tipi di documenti
DOCUMENT_PROCESSORS = {
    '.pdf': {
        'processor': 'extract_text_from_pdf',
        'chunk_method': 'chunk_text_with_pages',
        'preserve_pages': True
    },
    '.txt': {
        'processor': 'extract_text_from_txt',
        'chunk_method': 'chunk_text_simple',
        'preserve_pages': False
    },
    '.md': {
        'processor': 'extract_text_from_md',
        'chunk_method': 'chunk_text_simple',
        'preserve_pages': False
    },
    '.csv': {
        'processor': 'extract_text_from_csv',
        'chunk_method': 'chunk_text_simple',
        'preserve_pages': False
    },
    '.doc': {
        'processor': 'extract_text_from_doc',
        'chunk_method': 'chunk_text_simple',
        'preserve_pages': False
    },
    '.docx': {
        'processor': 'extract_text_from_docx',
        'chunk_method': 'chunk_text_simple',
        'preserve_pages': False
    }
}

# Configurazioni per il monitoraggio delle performance
PERFORMANCE_METRICS = {
    'track_processing_time': True,
    'track_memory_usage': True,
    'track_api_calls': True,
    'track_errors': True
}

# Configurazioni per la gestione degli errori
ERROR_HANDLING = {
    'max_file_errors': 10,  # Massimo numero di errori per file prima di saltarlo
    'continue_on_error': True,  # Continua l'elaborazione anche in caso di errori
    'retry_failed_files': True,  # Riprova i file falliti alla fine
    'error_report_threshold': 5  # Soglia di errori per generare un report dettagliato
}
