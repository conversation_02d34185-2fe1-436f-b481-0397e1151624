#!/usr/bin/env python3
"""
Sistema di scheduling per l'embedding automatizzato

Funzionalità:
- Scheduling periodico dell'embedding
- Configurazione flessibile degli orari
- Monitoraggio dello stato delle esecuzioni
- Gestione dei log e dei report
- Supporto per esecuzione come servizio

Utilizzo:
    python scripts/embedding_scheduler.py [opzioni]

Opzioni:
    --interval SECONDS   Intervallo in secondi tra le esecuzioni (default: 3600)
    --schedule TIME      Orario specifico per l'esecuzione (es. 02:00)
    --daemon            Esegue come daemon in background
    --config-file       File di configurazione del scheduling
    --status            Mostra lo stato del scheduler
    --stop              Ferma il scheduler in esecuzione
"""

import sys
import argparse
import time
import signal
import json
import subprocess
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging
import os

# Aggiungi il percorso root al sys.path
sys.path.append(str(Path(__file__).parent.parent))

from scripts.embedding_config import EmbeddingConfig
from scripts.embedding_utils import setup_logging

class ScheduleConfig:
    """Configurazione del scheduling"""
    
    def __init__(self, config_file: Optional[Path] = None):
        self.config_file = config_file or EmbeddingConfig.SCHEDULE_CONFIG_FILE
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Carica la configurazione del scheduling"""
        default_config = {
            "enabled": True,
            "interval_seconds": 3600,  # 1 ora
            "scheduled_times": [],  # Lista di orari specifici (es. ["02:00", "14:00"])
            "max_concurrent_jobs": 1,
            "retry_failed_jobs": True,
            "max_retries": 3,
            "retry_delay_seconds": 300,  # 5 minuti
            "log_retention_days": 30,
            "report_retention_days": 90,
            "embedding_options": {
                "background": True,
                "force_reprocess": False,
                "product_code": None
            }
        }
        
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    default_config.update(loaded_config)
            except Exception as e:
                logging.error(f"Errore nel caricamento configurazione: {e}")
        
        return default_config
    
    def save_config(self):
        """Salva la configurazione"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(f"Errore nel salvataggio configurazione: {e}")
    
    def get(self, key: str, default=None):
        """Ottiene un valore di configurazione"""
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any):
        """Imposta un valore di configurazione"""
        self.config[key] = value

class JobStatus:
    """Stato di un job di embedding"""
    
    def __init__(self):
        self.job_id = None
        self.start_time = None
        self.end_time = None
        self.status = "pending"  # pending, running, completed, failed
        self.exit_code = None
        self.output_file = None
        self.error_message = None
        self.retry_count = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte lo stato in dizionario"""
        return {
            'job_id': self.job_id,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status,
            'exit_code': self.exit_code,
            'output_file': self.output_file,
            'error_message': self.error_message,
            'retry_count': self.retry_count
        }

class EmbeddingScheduler:
    """Scheduler per l'embedding automatizzato"""
    
    def __init__(self, config: ScheduleConfig):
        self.config = config
        self.logger = setup_logging("embedding_scheduler")
        self.running = False
        self.current_jobs: Dict[str, JobStatus] = {}
        self.job_history: List[JobStatus] = []
        self.pid_file = Path("scheduler.pid")
        
        # Gestione segnali
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Gestisce i segnali di interruzione"""
        self.logger.info("Ricevuto segnale di interruzione. Arresto scheduler...")
        self.stop()
    
    def _create_pid_file(self):
        """Crea il file PID"""
        try:
            with open(self.pid_file, 'w') as f:
                f.write(str(os.getpid()))
        except Exception as e:
            self.logger.error(f"Errore nella creazione del file PID: {e}")
    
    def _remove_pid_file(self):
        """Rimuove il file PID"""
        try:
            if self.pid_file.exists():
                self.pid_file.unlink()
        except Exception as e:
            self.logger.error(f"Errore nella rimozione del file PID: {e}")
    
    def _is_running(self) -> bool:
        """Verifica se lo scheduler è già in esecuzione"""
        if not self.pid_file.exists():
            return False
        
        try:
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            # Verifica se il processo esiste
            os.kill(pid, 0)
            return True
        except (OSError, ValueError):
            # Il processo non esiste, rimuovi il file PID
            self._remove_pid_file()
            return False
    
    def _should_run_now(self) -> bool:
        """Determina se è il momento di eseguire l'embedding"""
        now = datetime.now()
        
        # Controlla orari specifici
        scheduled_times = self.config.get("scheduled_times", [])
        if scheduled_times:
            current_time = now.strftime("%H:%M")
            return current_time in scheduled_times
        
        # Usa intervallo
        return True  # Gestito dal loop principale
    
    def _create_job_id(self) -> str:
        """Crea un ID univoco per il job"""
        return f"embedding_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    def _run_embedding_job(self, job_id: str) -> JobStatus:
        """Esegue un job di embedding"""
        job = JobStatus()
        job.job_id = job_id
        job.start_time = datetime.now()
        job.status = "running"
        
        self.current_jobs[job_id] = job
        self.logger.info(f"Avvio job {job_id}")
        
        try:
            # Prepara comando
            script_path = Path(__file__).parent / "automated_embedding.py"
            cmd = [sys.executable, str(script_path)]
            
            # Aggiungi opzioni dalla configurazione
            embedding_options = self.config.get("embedding_options", {})
            if embedding_options.get("background"):
                cmd.append("--background")
            if embedding_options.get("force_reprocess"):
                cmd.append("--force-reprocess")
            if embedding_options.get("product_code"):
                cmd.extend(["--product-code", embedding_options["product_code"]])
            
            # Esegui comando
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=3600  # Timeout di 1 ora
            )
            
            job.end_time = datetime.now()
            job.exit_code = result.returncode
            job.status = "completed" if result.returncode == 0 else "failed"
            
            if result.returncode != 0:
                job.error_message = result.stderr
                self.logger.error(f"Job {job_id} fallito: {result.stderr}")
            else:
                self.logger.info(f"Job {job_id} completato con successo")
            
        except subprocess.TimeoutExpired:
            job.end_time = datetime.now()
            job.status = "failed"
            job.error_message = "Timeout scaduto"
            self.logger.error(f"Job {job_id} interrotto per timeout")
        
        except Exception as e:
            job.end_time = datetime.now()
            job.status = "failed"
            job.error_message = str(e)
            self.logger.error(f"Errore nell'esecuzione job {job_id}: {e}")
        
        finally:
            # Sposta il job dalla lista corrente alla cronologia
            if job_id in self.current_jobs:
                del self.current_jobs[job_id]
            self.job_history.append(job)
            
            # Mantieni solo gli ultimi 100 job nella cronologia
            if len(self.job_history) > 100:
                self.job_history = self.job_history[-100:]
        
        return job
    
    def _cleanup_old_files(self):
        """Pulisce file vecchi di log e report"""
        try:
            # Pulisci log vecchi
            log_retention = self.config.get("log_retention_days", 30)
            cutoff_date = datetime.now() - timedelta(days=log_retention)
            
            logs_dir = EmbeddingConfig.LOGS_DIR
            if logs_dir.exists():
                for log_file in logs_dir.glob("*.log"):
                    if datetime.fromtimestamp(log_file.stat().st_mtime) < cutoff_date:
                        log_file.unlink()
                        self.logger.debug(f"Rimosso log vecchio: {log_file}")
            
            # Pulisci report vecchi
            report_retention = self.config.get("report_retention_days", 90)
            cutoff_date = datetime.now() - timedelta(days=report_retention)
            
            reports_dir = EmbeddingConfig.REPORT_DIR
            if reports_dir.exists():
                for report_file in reports_dir.glob("*.json"):
                    if datetime.fromtimestamp(report_file.stat().st_mtime) < cutoff_date:
                        report_file.unlink()
                        self.logger.debug(f"Rimosso report vecchio: {report_file}")
        
        except Exception as e:
            self.logger.error(f"Errore nella pulizia file vecchi: {e}")
    
    def start(self, daemon: bool = False):
        """Avvia lo scheduler"""
        if self._is_running():
            self.logger.error("Scheduler già in esecuzione")
            return False
        
        self._create_pid_file()
        self.running = True
        
        if daemon:
            self.logger.info("Avvio scheduler in modalità daemon")
        else:
            self.logger.info("Avvio scheduler in modalità interattiva")
        
        try:
            interval = self.config.get("interval_seconds", 3600)
            last_cleanup = datetime.now()
            
            while self.running:
                try:
                    # Verifica se è il momento di eseguire
                    if self._should_run_now():
                        # Controlla il numero massimo di job concorrenti
                        max_concurrent = self.config.get("max_concurrent_jobs", 1)
                        if len(self.current_jobs) < max_concurrent:
                            job_id = self._create_job_id()
                            
                            # Esegui in thread separato per non bloccare lo scheduler
                            thread = threading.Thread(
                                target=self._run_embedding_job,
                                args=(job_id,)
                            )
                            thread.daemon = True
                            thread.start()
                    
                    # Pulizia periodica (una volta al giorno)
                    if datetime.now() - last_cleanup > timedelta(days=1):
                        self._cleanup_old_files()
                        last_cleanup = datetime.now()
                    
                    # Attendi prima del prossimo controllo
                    time.sleep(min(interval, 60))  # Controlla almeno ogni minuto
                
                except Exception as e:
                    self.logger.error(f"Errore nel loop dello scheduler: {e}")
                    time.sleep(60)  # Attendi un minuto prima di riprovare
        
        finally:
            self._remove_pid_file()
            self.logger.info("Scheduler arrestato")
    
    def stop(self):
        """Ferma lo scheduler"""
        self.running = False
    
    def get_status(self) -> Dict[str, Any]:
        """Ottiene lo stato dello scheduler"""
        return {
            "running": self.running,
            "is_process_running": self._is_running(),
            "current_jobs": {job_id: job.to_dict() for job_id, job in self.current_jobs.items()},
            "recent_jobs": [job.to_dict() for job in self.job_history[-10:]],
            "config": self.config.config
        }

def parse_arguments() -> argparse.Namespace:
    """Parsing degli argomenti da linea di comando"""
    parser = argparse.ArgumentParser(
        description="Sistema di scheduling per l'embedding automatizzato",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument(
        "--interval",
        type=int,
        default=3600,
        help="Intervallo in secondi tra le esecuzioni (default: 3600)"
    )
    
    parser.add_argument(
        "--schedule",
        type=str,
        help="Orario specifico per l'esecuzione (es. 02:00)"
    )
    
    parser.add_argument(
        "--daemon",
        action="store_true",
        help="Esegue come daemon in background"
    )
    
    parser.add_argument(
        "--config-file",
        type=Path,
        help="File di configurazione del scheduling"
    )
    
    parser.add_argument(
        "--status",
        action="store_true",
        help="Mostra lo stato del scheduler"
    )
    
    parser.add_argument(
        "--stop",
        action="store_true",
        help="Ferma il scheduler in esecuzione"
    )
    
    return parser.parse_args()

def main():
    """Funzione principale"""
    args = parse_arguments()
    
    # Carica configurazione
    config = ScheduleConfig(args.config_file)
    
    # Aggiorna configurazione con argomenti
    if args.interval != 3600:
        config.set("interval_seconds", args.interval)
    
    if args.schedule:
        scheduled_times = config.get("scheduled_times", [])
        if args.schedule not in scheduled_times:
            scheduled_times.append(args.schedule)
            config.set("scheduled_times", scheduled_times)
    
    config.save_config()
    
    # Crea scheduler
    scheduler = EmbeddingScheduler(config)
    
    if args.status:
        # Mostra stato
        status = scheduler.get_status()
        print(json.dumps(status, indent=2, ensure_ascii=False))
        return
    
    if args.stop:
        # Ferma scheduler
        if scheduler._is_running():
            try:
                with open(scheduler.pid_file, 'r') as f:
                    pid = int(f.read().strip())
                os.kill(pid, signal.SIGTERM)
                print("Segnale di arresto inviato allo scheduler")
            except Exception as e:
                print(f"Errore nell'arresto dello scheduler: {e}")
        else:
            print("Scheduler non in esecuzione")
        return
    
    # Avvia scheduler
    try:
        scheduler.start(daemon=args.daemon)
    except KeyboardInterrupt:
        print("\nArresto scheduler...")
        scheduler.stop()

if __name__ == "__main__":
    main()
