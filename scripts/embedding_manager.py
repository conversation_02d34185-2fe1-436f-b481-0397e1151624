#!/usr/bin/env python3
"""
Manager per il sistema di embedding automatizzato

Questo script fornisce un'interfaccia unificata per gestire tutte le operazioni
di embedding, scheduling e monitoraggio.

Utilizzo:
    python scripts/embedding_manager.py <comando> [opzioni]

Comandi disponibili:
    run         Esegue l'embedding una volta
    schedule    Gestisce lo scheduling automatico
    status      Mostra lo stato del sistema
    reports     Gestisce i report
    cleanup     Pulisce file temporanei e vecchi
    config      Gestisce la configurazione
    help        Mostra questo aiuto

Esempi:
    # Esegue embedding una volta
    python scripts/embedding_manager.py run --force-reprocess
    
    # Avvia scheduling automatico
    python scripts/embedding_manager.py schedule start --interval 3600
    
    # Mostra stato del sistema
    python scripts/embedding_manager.py status
    
    # Mostra report recenti
    python scripts/embedding_manager.py reports list --last 5
"""

import sys
import argparse
import json
import subprocess
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

# Aggiungi il percorso root al sys.path
sys.path.append(str(Path(__file__).parent.parent))

from scripts.embedding_config import EmbeddingConfig
from scripts.embedding_utils import setup_logging, validate_environment
from scripts.embedding_scheduler import ScheduleConfig, EmbeddingScheduler

class EmbeddingManager:
    """Manager principale per il sistema di embedding"""
    
    def __init__(self):
        self.config = EmbeddingConfig()
        self.logger = setup_logging("embedding_manager")
        self.schedule_config = ScheduleConfig()
    
    def run_embedding(self, args: argparse.Namespace) -> bool:
        """Esegue l'embedding una volta"""
        print("🚀 Avvio embedding...")
        
        # Prepara comando
        script_path = Path(__file__).parent / "automated_embedding.py"
        cmd = [sys.executable, str(script_path)]
        
        # Aggiungi opzioni
        if args.force_reprocess:
            cmd.append("--force-reprocess")
        if args.dry_run:
            cmd.append("--dry-run")
        if args.product_code:
            cmd.extend(["--product-code", args.product_code])
        if args.background:
            cmd.append("--background")
        if args.verbose:
            cmd.append("--verbose")
        
        try:
            result = subprocess.run(cmd, check=False)
            return result.returncode == 0
        except Exception as e:
            self.logger.error(f"Errore nell'esecuzione embedding: {e}")
            return False
    
    def manage_schedule(self, args: argparse.Namespace) -> bool:
        """Gestisce lo scheduling"""
        action = args.schedule_action
        
        if action == "start":
            return self._start_scheduler(args)
        elif action == "stop":
            return self._stop_scheduler()
        elif action == "status":
            return self._show_scheduler_status()
        elif action == "config":
            return self._configure_scheduler(args)
        else:
            print(f"Azione scheduling non riconosciuta: {action}")
            return False
    
    def _start_scheduler(self, args: argparse.Namespace) -> bool:
        """Avvia lo scheduler"""
        print("🕐 Avvio scheduler...")
        
        # Aggiorna configurazione se necessario
        if hasattr(args, 'interval') and args.interval:
            self.schedule_config.set("interval_seconds", args.interval)
        if hasattr(args, 'schedule_time') and args.schedule_time:
            times = self.schedule_config.get("scheduled_times", [])
            if args.schedule_time not in times:
                times.append(args.schedule_time)
                self.schedule_config.set("scheduled_times", times)
        
        self.schedule_config.save_config()
        
        # Avvia scheduler
        script_path = Path(__file__).parent / "embedding_scheduler.py"
        cmd = [sys.executable, str(script_path)]
        
        if hasattr(args, 'daemon') and args.daemon:
            cmd.append("--daemon")
        
        try:
            if hasattr(args, 'daemon') and args.daemon:
                # Avvia in background
                subprocess.Popen(cmd, start_new_session=True)
                print("✅ Scheduler avviato in background")
            else:
                # Avvia in foreground
                subprocess.run(cmd)
            return True
        except Exception as e:
            self.logger.error(f"Errore nell'avvio scheduler: {e}")
            return False
    
    def _stop_scheduler(self) -> bool:
        """Ferma lo scheduler"""
        print("🛑 Arresto scheduler...")
        
        script_path = Path(__file__).parent / "embedding_scheduler.py"
        cmd = [sys.executable, str(script_path), "--stop"]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            print(result.stdout)
            if result.stderr:
                print(result.stderr)
            return result.returncode == 0
        except Exception as e:
            self.logger.error(f"Errore nell'arresto scheduler: {e}")
            return False
    
    def _show_scheduler_status(self) -> bool:
        """Mostra lo stato dello scheduler"""
        script_path = Path(__file__).parent / "embedding_scheduler.py"
        cmd = [sys.executable, str(script_path), "--status"]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                status = json.loads(result.stdout)
                self._print_scheduler_status(status)
            else:
                print("Errore nel recupero dello stato")
                print(result.stderr)
            return result.returncode == 0
        except Exception as e:
            self.logger.error(f"Errore nel recupero stato scheduler: {e}")
            return False
    
    def _print_scheduler_status(self, status: Dict[str, Any]):
        """Stampa lo stato dello scheduler in formato leggibile"""
        print("\n📊 STATO SCHEDULER")
        print("=" * 50)
        
        is_running = status.get("is_process_running", False)
        print(f"🔄 Stato: {'🟢 Attivo' if is_running else '🔴 Inattivo'}")
        
        config = status.get("config", {})
        interval = config.get("interval_seconds", 0)
        if interval > 0:
            print(f"⏱️  Intervallo: {interval} secondi ({interval//3600}h {(interval%3600)//60}m)")
        
        scheduled_times = config.get("scheduled_times", [])
        if scheduled_times:
            print(f"🕐 Orari programmati: {', '.join(scheduled_times)}")
        
        current_jobs = status.get("current_jobs", {})
        if current_jobs:
            print(f"🏃 Job in esecuzione: {len(current_jobs)}")
            for job_id, job in current_jobs.items():
                print(f"   • {job_id}: {job.get('status', 'unknown')}")
        
        recent_jobs = status.get("recent_jobs", [])
        if recent_jobs:
            print(f"\n📋 Ultimi job ({len(recent_jobs)}):")
            for job in recent_jobs[-5:]:  # Mostra solo gli ultimi 5
                start_time = job.get("start_time", "")
                if start_time:
                    start_time = datetime.fromisoformat(start_time).strftime("%d/%m %H:%M")
                status_emoji = {"completed": "✅", "failed": "❌", "running": "🏃"}.get(job.get("status"), "❓")
                print(f"   {status_emoji} {job.get('job_id', 'unknown')} - {start_time}")
    
    def _configure_scheduler(self, args: argparse.Namespace) -> bool:
        """Configura lo scheduler"""
        print("⚙️  Configurazione scheduler...")
        
        # Implementa configurazione interattiva o da argomenti
        # Per ora mostra la configurazione corrente
        config = self.schedule_config.config
        print(json.dumps(config, indent=2, ensure_ascii=False))
        return True
    
    def show_status(self, args: argparse.Namespace) -> bool:
        """Mostra lo stato generale del sistema"""
        print("\n🔍 STATO SISTEMA EMBEDDING")
        print("=" * 60)
        
        # Valida ambiente
        env_errors = validate_environment()
        if env_errors:
            print("❌ Problemi di configurazione:")
            for error in env_errors:
                print(f"   • {error}")
        else:
            print("✅ Configurazione ambiente: OK")
        
        # Stato directory
        pdf_dir = self.config.PDF_BASE_DIR
        if pdf_dir.exists():
            file_count = len(list(pdf_dir.rglob("*")))
            print(f"📁 Directory PDF: {pdf_dir} ({file_count} file)")
        else:
            print(f"❌ Directory PDF non trovata: {pdf_dir}")
        
        # Stato database
        db_path = self.config.CHROMADB_PATH
        if db_path.exists():
            print(f"💾 Database ChromaDB: {db_path}")
        else:
            print(f"⚠️  Database ChromaDB non inizializzato: {db_path}")
        
        # Stato metadati
        metadata_file = self.config.METADATA_FILE
        if metadata_file.exists():
            try:
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                file_count = len(metadata.get("files", {}))
                last_processed = metadata.get("last_processed", "Mai")
                print(f"📋 Metadati: {file_count} file tracciati, ultimo aggiornamento: {last_processed}")
            except Exception as e:
                print(f"⚠️  Errore lettura metadati: {e}")
        else:
            print("📋 Metadati: Non inizializzati")
        
        # Stato scheduler
        print("\n🕐 Stato Scheduler:")
        self._show_scheduler_status()
        
        return True
    
    def manage_reports(self, args: argparse.Namespace) -> bool:
        """Gestisce i report"""
        action = args.report_action
        
        if action == "list":
            return self._list_reports(args)
        elif action == "show":
            return self._show_report(args)
        elif action == "cleanup":
            return self._cleanup_reports(args)
        else:
            print(f"Azione report non riconosciuta: {action}")
            return False
    
    def _list_reports(self, args: argparse.Namespace) -> bool:
        """Lista i report disponibili"""
        reports_dir = self.config.REPORT_DIR
        if not reports_dir.exists():
            print("📋 Nessun report trovato")
            return True
        
        reports = list(reports_dir.glob("*.json"))
        reports.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        limit = getattr(args, 'last', None)
        if limit:
            reports = reports[:limit]
        
        print(f"\n📋 REPORT DISPONIBILI ({len(reports)})")
        print("=" * 50)
        
        for report_file in reports:
            try:
                stat = report_file.stat()
                size = stat.st_size
                modified = datetime.fromtimestamp(stat.st_mtime)
                print(f"📄 {report_file.name}")
                print(f"   📅 {modified.strftime('%d/%m/%Y %H:%M:%S')}")
                print(f"   💾 {size} bytes")
                print()
            except Exception as e:
                print(f"❌ Errore lettura {report_file.name}: {e}")
        
        return True
    
    def _show_report(self, args: argparse.Namespace) -> bool:
        """Mostra un report specifico"""
        if not hasattr(args, 'report_file') or not args.report_file:
            print("❌ Specificare il file del report")
            return False
        
        report_path = self.config.REPORT_DIR / args.report_file
        if not report_path.exists():
            print(f"❌ Report non trovato: {report_path}")
            return False
        
        try:
            with open(report_path, 'r', encoding='utf-8') as f:
                report = json.load(f)
            
            print(f"\n📊 REPORT: {args.report_file}")
            print("=" * 60)
            print(json.dumps(report, indent=2, ensure_ascii=False))
            return True
        except Exception as e:
            print(f"❌ Errore lettura report: {e}")
            return False
    
    def _cleanup_reports(self, args: argparse.Namespace) -> bool:
        """Pulisce i report vecchi"""
        days = getattr(args, 'older_than', 30)
        cutoff_date = datetime.now() - timedelta(days=days)
        
        reports_dir = self.config.REPORT_DIR
        if not reports_dir.exists():
            print("📋 Nessun report da pulire")
            return True
        
        removed_count = 0
        for report_file in reports_dir.glob("*.json"):
            try:
                if datetime.fromtimestamp(report_file.stat().st_mtime) < cutoff_date:
                    report_file.unlink()
                    removed_count += 1
                    print(f"🗑️  Rimosso: {report_file.name}")
            except Exception as e:
                print(f"❌ Errore rimozione {report_file.name}: {e}")
        
        print(f"✅ Rimossi {removed_count} report vecchi")
        return True
    
    def cleanup_system(self, args: argparse.Namespace) -> bool:
        """Pulisce il sistema"""
        print("🧹 Pulizia sistema...")
        
        # Pulisci log vecchi
        days = getattr(args, 'older_than', 30)
        cutoff_date = datetime.now() - timedelta(days=days)
        
        logs_dir = self.config.LOGS_DIR
        removed_logs = 0
        if logs_dir.exists():
            for log_file in logs_dir.glob("*.log"):
                try:
                    if datetime.fromtimestamp(log_file.stat().st_mtime) < cutoff_date:
                        log_file.unlink()
                        removed_logs += 1
                except Exception as e:
                    print(f"❌ Errore rimozione log {log_file.name}: {e}")
        
        # Pulisci report vecchi
        self._cleanup_reports(args)
        
        print(f"✅ Pulizia completata: {removed_logs} log rimossi")
        return True

def create_parser() -> argparse.ArgumentParser:
    """Crea il parser degli argomenti"""
    parser = argparse.ArgumentParser(
        description="Manager per il sistema di embedding automatizzato",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Comandi disponibili")
    
    # Comando run
    run_parser = subparsers.add_parser("run", help="Esegue l'embedding una volta")
    run_parser.add_argument("--force-reprocess", action="store_true", help="Riprocessa tutti i file")
    run_parser.add_argument("--dry-run", action="store_true", help="Simula l'esecuzione")
    run_parser.add_argument("--product-code", help="Processa solo un prodotto specifico")
    run_parser.add_argument("--background", action="store_true", help="Esegue in background")
    run_parser.add_argument("--verbose", action="store_true", help="Output dettagliato")
    
    # Comando schedule
    schedule_parser = subparsers.add_parser("schedule", help="Gestisce lo scheduling")
    schedule_subparsers = schedule_parser.add_subparsers(dest="schedule_action")
    
    start_parser = schedule_subparsers.add_parser("start", help="Avvia scheduler")
    start_parser.add_argument("--interval", type=int, help="Intervallo in secondi")
    start_parser.add_argument("--schedule-time", help="Orario specifico (HH:MM)")
    start_parser.add_argument("--daemon", action="store_true", help="Esegue come daemon")
    
    schedule_subparsers.add_parser("stop", help="Ferma scheduler")
    schedule_subparsers.add_parser("status", help="Stato scheduler")
    schedule_subparsers.add_parser("config", help="Configura scheduler")
    
    # Comando status
    subparsers.add_parser("status", help="Mostra lo stato del sistema")
    
    # Comando reports
    reports_parser = subparsers.add_parser("reports", help="Gestisce i report")
    reports_subparsers = reports_parser.add_subparsers(dest="report_action")
    
    list_parser = reports_subparsers.add_parser("list", help="Lista report")
    list_parser.add_argument("--last", type=int, help="Mostra solo gli ultimi N report")
    
    show_parser = reports_subparsers.add_parser("show", help="Mostra report specifico")
    show_parser.add_argument("report_file", help="Nome del file del report")
    
    cleanup_parser = reports_subparsers.add_parser("cleanup", help="Pulisce report vecchi")
    cleanup_parser.add_argument("--older-than", type=int, default=30, help="Giorni (default: 30)")
    
    # Comando cleanup
    cleanup_parser = subparsers.add_parser("cleanup", help="Pulisce file temporanei")
    cleanup_parser.add_argument("--older-than", type=int, default=30, help="Giorni (default: 30)")
    
    # Comando config
    subparsers.add_parser("config", help="Gestisce la configurazione")
    
    return parser

def main():
    """Funzione principale"""
    parser = create_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    manager = EmbeddingManager()
    
    try:
        if args.command == "run":
            success = manager.run_embedding(args)
        elif args.command == "schedule":
            success = manager.manage_schedule(args)
        elif args.command == "status":
            success = manager.show_status(args)
        elif args.command == "reports":
            success = manager.manage_reports(args)
        elif args.command == "cleanup":
            success = manager.cleanup_system(args)
        elif args.command == "config":
            print("Configurazione non ancora implementata")
            success = True
        else:
            print(f"Comando non riconosciuto: {args.command}")
            success = False
        
        sys.exit(0 if success else 1)
    
    except KeyboardInterrupt:
        print("\n⚠️  Operazione interrotta dall'utente")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Errore: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
