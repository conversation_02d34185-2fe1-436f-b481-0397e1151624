#!/usr/bin/env python3
"""
Script di setup per il sistema di embedding automatizzato

Questo script configura automaticamente tutto il necessario per il sistema
di embedding, incluse directory, dipendenze e configurazioni iniziali.

Utilizzo:
    python scripts/setup_embedding_system.py [opzioni]

Opzioni:
    --check-only    Verifica solo la configurazione senza modificare nulla
    --force         Forza la reinstallazione anche se già configurato
    --no-deps       Non installa dipendenze Python
    --config-only   Configura solo i file di configurazione
"""

import sys
import subprocess
import argparse
import json
import os
from pathlib import Path
from typing import List, Dict, Any, Tuple

# Aggiungi il percorso root al sys.path
sys.path.append(str(Path(__file__).parent.parent))

class EmbeddingSystemSetup:
    """Setup del sistema di embedding"""
    
    def __init__(self, args: argparse.Namespace):
        self.args = args
        self.root_dir = Path(__file__).parent.parent
        self.issues: List[str] = []
        self.fixes_applied: List[str] = []
    
    def check_python_version(self) -> bool:
        """Verifica la versione di Python"""
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            self.issues.append(f"Python 3.8+ richiesto, trovato {version.major}.{version.minor}")
            return False
        
        print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
        return True
    
    def check_dependencies(self) -> Tuple[bool, List[str]]:
        """Verifica le dipendenze Python"""
        required_packages = [
            'chromadb',
            'requests', 
            'PyMuPDF',
            'google-generativeai',
            'python-dotenv',
            'langdetect',
            'presidio-analyzer',
            'presidio-anonymizer',
            'mysql-connector-python',
            'psutil',
            'tqdm'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                print(f"✅ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"❌ {package} - MANCANTE")
        
        if missing_packages:
            self.issues.append(f"Pacchetti Python mancanti: {', '.join(missing_packages)}")
        
        return len(missing_packages) == 0, missing_packages
    
    def install_dependencies(self, missing_packages: List[str]) -> bool:
        """Installa le dipendenze mancanti"""
        if not missing_packages:
            return True
        
        print(f"📦 Installazione di {len(missing_packages)} pacchetti...")
        
        try:
            cmd = [sys.executable, "-m", "pip", "install"] + missing_packages
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print("✅ Dipendenze installate con successo")
            self.fixes_applied.append(f"Installati pacchetti: {', '.join(missing_packages)}")
            return True
        except subprocess.CalledProcessError as e:
            self.issues.append(f"Errore installazione dipendenze: {e}")
            print(f"❌ Errore installazione: {e}")
            return False
    
    def check_directories(self) -> bool:
        """Verifica e crea le directory necessarie"""
        required_dirs = [
            'pdf',
            'logs', 
            'reports',
            'chromadb_data'
        ]
        
        all_good = True
        
        for dir_name in required_dirs:
            dir_path = self.root_dir / dir_name
            if dir_path.exists():
                print(f"✅ Directory {dir_name}/")
            else:
                if not self.args.check_only:
                    dir_path.mkdir(exist_ok=True)
                    print(f"📁 Creata directory {dir_name}/")
                    self.fixes_applied.append(f"Creata directory {dir_name}/")
                else:
                    print(f"❌ Directory mancante: {dir_name}/")
                    self.issues.append(f"Directory mancante: {dir_name}/")
                    all_good = False
        
        return all_good
    
    def check_api_keys(self) -> bool:
        """Verifica le API keys"""
        required_keys = ['JINA_API_KEY', 'GEMINI_API_KEY']
        missing_keys = []
        
        for key in required_keys:
            value = os.getenv(key)
            if value:
                print(f"✅ {key} configurata")
            else:
                print(f"⚠️  {key} non configurata")
                missing_keys.append(key)
        
        if missing_keys:
            self.issues.append(f"API keys mancanti: {', '.join(missing_keys)}")
            return False
        
        return True
    
    def create_sample_config(self) -> bool:
        """Crea configurazioni di esempio"""
        configs_to_create = []
        
        # Configurazione scheduling
        schedule_config_file = self.root_dir / "schedule_config.json"
        if not schedule_config_file.exists():
            schedule_config = {
                "enabled": True,
                "interval_seconds": 3600,
                "scheduled_times": [],
                "max_concurrent_jobs": 1,
                "retry_failed_jobs": True,
                "max_retries": 3,
                "retry_delay_seconds": 300,
                "log_retention_days": 30,
                "report_retention_days": 90,
                "embedding_options": {
                    "background": True,
                    "force_reprocess": False,
                    "product_code": None
                }
            }
            configs_to_create.append((schedule_config_file, schedule_config))
        
        # File .env di esempio
        env_example_file = self.root_dir / ".env.example"
        if not env_example_file.exists():
            env_content = """# API Keys per il sistema di embedding
JINA_API_KEY=your_jina_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here

# Configurazioni opzionali
# LOG_LEVEL=INFO
# CHUNK_SIZE=1000
# BATCH_SIZE=50
"""
            configs_to_create.append((env_example_file, env_content))
        
        # Crea i file di configurazione
        for config_file, config_content in configs_to_create:
            if not self.args.check_only:
                try:
                    if isinstance(config_content, dict):
                        with open(config_file, 'w', encoding='utf-8') as f:
                            json.dump(config_content, f, indent=2, ensure_ascii=False)
                    else:
                        with open(config_file, 'w', encoding='utf-8') as f:
                            f.write(config_content)
                    
                    print(f"📝 Creato file di configurazione: {config_file.name}")
                    self.fixes_applied.append(f"Creato {config_file.name}")
                except Exception as e:
                    self.issues.append(f"Errore creazione {config_file.name}: {e}")
                    return False
            else:
                print(f"📝 Da creare: {config_file.name}")
        
        return True
    
    def create_sample_documents(self) -> bool:
        """Crea documenti di esempio per test"""
        if self.args.check_only:
            return True
        
        # Crea directory prodotto di esempio
        sample_product_dir = self.root_dir / "pdf" / "ESEMPIO_PRODOTTO"
        if not sample_product_dir.exists():
            sample_product_dir.mkdir(parents=True)
            
            # Crea file di esempio
            sample_files = {
                "manuale_utente.txt": """Manuale Utente - Esempio Prodotto

Questo è un documento di esempio per testare il sistema di embedding.

Caratteristiche principali:
- Funzionalità A: Descrizione della funzionalità A
- Funzionalità B: Descrizione della funzionalità B
- Funzionalità C: Descrizione della funzionalità C

Installazione:
1. Scaricare il software
2. Eseguire l'installer
3. Seguire le istruzioni a schermo

Configurazione:
- Impostare i parametri di base
- Configurare le connessioni
- Testare il funzionamento

Per supporto tecnico, contattare il team di assistenza.
""",
                "specifiche_tecniche.md": """# Specifiche Tecniche - Esempio Prodotto

## Requisiti di Sistema

### Hardware
- CPU: Processore dual-core 2.0 GHz o superiore
- RAM: 4 GB minimo, 8 GB raccomandato
- Spazio disco: 2 GB disponibili

### Software
- Sistema operativo: Windows 10/11, macOS 10.15+, Linux Ubuntu 18.04+
- Framework: .NET 6.0 o superiore
- Database: MySQL 8.0 o PostgreSQL 12+

## Architettura

Il sistema è basato su un'architettura modulare che comprende:

1. **Modulo Core**: Gestisce la logica di business principale
2. **Modulo API**: Fornisce interfacce REST per l'integrazione
3. **Modulo Database**: Gestisce la persistenza dei dati
4. **Modulo UI**: Interfaccia utente web responsive

## Configurazione

### Parametri principali
- `max_connections`: Numero massimo di connessioni simultanee
- `timeout`: Timeout per le operazioni in secondi
- `log_level`: Livello di logging (DEBUG, INFO, WARNING, ERROR)

### File di configurazione
Il sistema utilizza file JSON per la configurazione:
```json
{
  "database": {
    "host": "localhost",
    "port": 3306,
    "name": "esempio_db"
  },
  "api": {
    "port": 8080,
    "ssl_enabled": true
  }
}
```
"""
            }
            
            for filename, content in sample_files.items():
                file_path = sample_product_dir / filename
                file_path.write_text(content, encoding='utf-8')
            
            print(f"📄 Creati documenti di esempio in {sample_product_dir}")
            self.fixes_applied.append("Creati documenti di esempio")
        
        return True
    
    def run_tests(self) -> bool:
        """Esegue test di base del sistema"""
        if self.args.check_only:
            return True
        
        print("🧪 Esecuzione test di base...")
        
        try:
            test_script = self.root_dir / "scripts" / "test_embedding_system.py"
            if test_script.exists():
                result = subprocess.run(
                    [sys.executable, str(test_script), "--quick"],
                    capture_output=True,
                    text=True,
                    timeout=60
                )
                
                if result.returncode == 0:
                    print("✅ Test di base superati")
                    return True
                else:
                    print("⚠️  Alcuni test sono falliti")
                    print(result.stdout)
                    return False
            else:
                print("⚠️  Script di test non trovato")
                return False
        
        except subprocess.TimeoutExpired:
            print("⚠️  Test interrotti per timeout")
            return False
        except Exception as e:
            print(f"❌ Errore durante i test: {e}")
            return False
    
    def print_summary(self):
        """Stampa il riassunto del setup"""
        print("\n" + "="*60)
        print("📊 RIASSUNTO SETUP SISTEMA EMBEDDING")
        print("="*60)
        
        if self.fixes_applied:
            print("✅ Correzioni applicate:")
            for fix in self.fixes_applied:
                print(f"   • {fix}")
        
        if self.issues:
            print("\n⚠️  Problemi rilevati:")
            for issue in self.issues:
                print(f"   • {issue}")
        
        if not self.issues:
            print("🎉 Sistema configurato correttamente!")
            print("\nProssimi passi:")
            print("1. Configura le API keys nel file .env")
            print("2. Aggiungi i tuoi documenti nella directory pdf/")
            print("3. Esegui: python scripts/embedding_manager.py run")
        else:
            print("\n🔧 Risolvi i problemi sopra elencati prima di procedere")
        
        print("="*60)
    
    def run_setup(self) -> bool:
        """Esegue il setup completo"""
        print("🚀 Setup sistema di embedding automatizzato")
        print("="*50)
        
        success = True
        
        # Verifica Python
        if not self.check_python_version():
            success = False
        
        # Verifica e installa dipendenze
        deps_ok, missing_deps = self.check_dependencies()
        if not deps_ok and not self.args.no_deps and not self.args.check_only:
            if not self.install_dependencies(missing_deps):
                success = False
        elif not deps_ok:
            success = False
        
        # Verifica directory
        if not self.check_directories():
            success = False
        
        # Verifica API keys
        self.check_api_keys()  # Non blocca il setup
        
        # Crea configurazioni
        if not self.args.check_only:
            if not self.create_sample_config():
                success = False
            
            if not self.create_sample_documents():
                success = False
        
        # Esegui test se non in modalità check-only
        if success and not self.args.check_only and not self.args.config_only:
            self.run_tests()
        
        return success

def parse_arguments() -> argparse.Namespace:
    """Parsing degli argomenti da linea di comando"""
    parser = argparse.ArgumentParser(
        description="Setup del sistema di embedding automatizzato",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument(
        "--check-only",
        action="store_true",
        help="Verifica solo la configurazione senza modificare nulla"
    )
    
    parser.add_argument(
        "--force",
        action="store_true", 
        help="Forza la reinstallazione anche se già configurato"
    )
    
    parser.add_argument(
        "--no-deps",
        action="store_true",
        help="Non installa dipendenze Python"
    )
    
    parser.add_argument(
        "--config-only",
        action="store_true",
        help="Configura solo i file di configurazione"
    )
    
    return parser.parse_args()

def main():
    """Funzione principale"""
    args = parse_arguments()
    
    setup = EmbeddingSystemSetup(args)
    
    try:
        success = setup.run_setup()
        setup.print_summary()
        
        sys.exit(0 if success else 1)
    
    except KeyboardInterrupt:
        print("\n⚠️  Setup interrotto dall'utente")
        sys.exit(1)
    
    except Exception as e:
        print(f"❌ Errore durante il setup: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
