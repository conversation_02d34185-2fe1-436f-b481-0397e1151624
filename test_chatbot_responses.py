#!/usr/bin/env python3
"""
Script per testare le risposte del chatbot con il nuovo prompt
"""

import os
import sys
from dotenv import load_dotenv

# Carica le variabili d'ambiente
load_dotenv()

def test_chatbot_if_configured():
    """Testa il chatbot solo se le API key sono configurate"""
    
    jina_api_key = os.getenv("JINA_API_KEY")
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    
    if not jina_api_key or not gemini_api_key:
        print("⚠️  API keys non configurate. Saltando il test del chatbot.")
        print("   Per testare il chatbot, configura JINA_API_KEY e GEMINI_API_KEY nel file .env")
        return False
    
    try:
        from pdf_chatbot_prodotti import ProductChatbot
        
        print("🤖 Inizializzazione del chatbot...")
        chatbot = ProductChatbot(
            jina_api_key=jina_api_key,
            gemini_api_key=gemini_api_key,
            verbosity_level=3
        )
        
        # Test con una domanda generica (senza documenti specifici)
        test_query = "Come funziona questo prodotto?"
        test_product_code = "TEST_PRODUCT"
        
        print(f"🔍 Test query: {test_query}")
        print(f"📦 Product code: {test_product_code}")
        
        response, _ = chatbot.search_and_answer(test_query, test_product_code)
        
        print("\n📝 Risposta del chatbot:")
        print("-" * 40)
        print(response)
        print("-" * 40)
        
        # Verifica che la risposta non contenga frasi problematiche
        problematic_phrases = [
            "gentile utente",
            "in base alla documentazione",
            "in base ai documenti",
            "secondo i manuali",
            "dalle informazioni",
            "in base al contesto"
        ]
        
        response_lower = response.lower()
        found_issues = []
        
        for phrase in problematic_phrases:
            if phrase in response_lower:
                found_issues.append(phrase)
        
        if found_issues:
            print(f"⚠️  Trovate frasi problematiche: {found_issues}")
            return False
        else:
            print("✅ Nessuna frase problematica trovata!")
            return True
            
    except Exception as e:
        print(f"❌ Errore durante il test del chatbot: {e}")
        return False

def main():
    """Funzione principale"""
    print("🧪 Test delle risposte del chatbot")
    print("=" * 50)
    
    success = test_chatbot_if_configured()
    
    print("\n📋 Riepilogo delle modifiche apportate:")
    print("1. ✅ Modificato il prompt principale per essere più specifico")
    print("2. ✅ Aggiunti esempi concreti di cosa evitare")
    print("3. ✅ Implementata funzione di post-processing _clean_ai_references()")
    print("4. ✅ Modificato il messaggio di errore quando non ci sono documenti")
    
    print("\n🎯 Risultato:")
    if success:
        print("✅ Il chatbot ora dovrebbe rispondere come un consulente tecnico esperto!")
    else:
        print("⚠️  Verifica la configurazione o testa manualmente il chatbot.")
    
    print("\n💡 Per testare completamente:")
    print("1. Configura le API keys nel file .env")
    print("2. Aggiungi documenti PDF nella cartella pdf/CODICE_PRODOTTO/")
    print("3. Avvia l'applicazione con: python app.py")
    print("4. Testa le risposte nell'interfaccia web")

if __name__ == "__main__":
    main()
