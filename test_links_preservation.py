#!/usr/bin/env python3
"""
Test per verificare che i link ai documenti vengano preservati correttamente
nelle risposte dell'assistente virtuale.
"""

import os
import sys
from dotenv import load_dotenv
from pdf_chatbot_prodotti import ProductChatbot

# Carica le variabili d'ambiente
load_dotenv()

def test_links_preservation():
    """Test per verificare che i link ai documenti vengano preservati"""
    
    # Inizializza il chatbot
    jina_api_key = os.getenv("JINA_API_KEY")
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    
    if not jina_api_key or not gemini_api_key:
        print("❌ API keys mancanti nel file .env")
        return False
    
    try:
        print("🤖 Inizializzazione del chatbot...")
        chatbot = ProductChatbot(
            jina_api_key=jina_api_key,
            gemini_api_key=gemini_api_key,
            verbosity_level=5
        )
        
        # Prepara i documenti per ProdottoA
        product_code = "ProdottoA"
        print(f"📄 Preparazione documenti per {product_code}...")
        documents_ready = chatbot.prepare_product_documents(product_code)
        
        if not documents_ready:
            print(f"❌ Impossibile preparare i documenti per {product_code}")
            return False
        
        print("✅ Documenti pronti!")
        
        # Test query che dovrebbe generare riferimenti ai documenti
        test_query = "Come faccio la verifica visiva del sensore?"
        print(f"🔍 Test query: {test_query}")
        
        # Esegui la query
        answer, history = chatbot.search_and_answer(test_query, product_code)
        
        print("\n" + "="*60)
        print("📝 RISPOSTA RICEVUTA:")
        print("="*60)
        print(answer)
        print("="*60)
        
        # Verifica che ci siano link ai documenti nella risposta
        has_links = "[📄" in answer and "](" in answer
        
        if has_links:
            print("✅ TEST PASSATO: I link ai documenti sono presenti nella risposta!")
            
            # Conta i link trovati
            import re
            link_pattern = r'\[📄[^\]]+\]\([^)]+\)'
            links = re.findall(link_pattern, answer)
            print(f"🔗 Trovati {len(links)} link ai documenti:")
            for i, link in enumerate(links, 1):
                print(f"   {i}. {link}")
            
            return True
        else:
            print("❌ TEST FALLITO: Nessun link ai documenti trovato nella risposta!")
            print("🔍 Verifica se la risposta contiene riferimenti ai documenti senza link...")
            
            # Cerca pattern che indicano riferimenti ai documenti senza link
            doc_references = []
            if "manuale" in answer.lower():
                doc_references.append("Riferimento a 'manuale'")
            if "documento" in answer.lower():
                doc_references.append("Riferimento a 'documento'")
            if "pag." in answer.lower() or "pagina" in answer.lower():
                doc_references.append("Riferimento a pagina")
            
            if doc_references:
                print("⚠️  Trovati riferimenti ai documenti ma senza link:")
                for ref in doc_references:
                    print(f"   - {ref}")
                print("🔧 Questo indica che i link sono stati rimossi durante il processing!")
            
            return False
            
    except Exception as e:
        print(f"❌ Errore durante il test: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Test di preservazione dei link ai documenti")
    print("="*50)
    
    success = test_links_preservation()
    
    print("\n" + "="*50)
    if success:
        print("🎉 TEST COMPLETATO CON SUCCESSO!")
    else:
        print("💥 TEST FALLITO!")
    
    sys.exit(0 if success else 1)
